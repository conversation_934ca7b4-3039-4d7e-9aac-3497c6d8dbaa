package com.pay.manage.sync.mq.listener;

import com.alibaba.fastjson.JSON;
import com.pay.manage.sync.biz.lklsm.LklSmTransDataSyncBiz;
import com.pay.manage.sync.biz.lklsm.LklSmTransFeeDataSyncBiz;
import com.pay.manage.sync.mq.constant.PublishTopicConstant;
import com.pay.manage.sync.mq.sync.LklSmTransFeeNotifyDto;
import com.pay.manage.sync.mq.sync.LklSmTransNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.starter.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.starter.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * LKLSM交易数据同步
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = PublishTopicConstant.PC_TRANS_LKLSM_TOPIC,
        consumerGroup = PublishTopicConstant.PC_TRANS_LKLSM_TOPIC + PublishTopicConstant.SYNC_GROUP,
        consumeThreadMax = 20)
public class LklSmTransSyncConsumer implements RocketMQListener<LklSmTransNotifyDto> {

    @Autowired
    private LklSmTransDataSyncBiz lklSmTransDataSyncBiz;

    @Override
    public void onMessage(LklSmTransNotifyDto lklSmTransNotify) {
        log.info("PC_TRANS_LKLSM_TOPIC on message {}", JSON.toJSONString(lklSmTransNotify));
        lklSmTransDataSyncBiz.dataSyncImport(lklSmTransNotify);
    }
}
