package com.pay.manage.sync.mapper;

import com.pay.manage.sync.entity.TransExtendFlowEntity;
import org.apache.ibatis.annotations.Param;

public interface TransExtendFlowSyncMapper {

    int insert(TransExtendFlowEntity extendFlow);

    TransExtendFlowEntity findByFlowId(String flowId);

    int updateTransByIdForSync(TransExtendFlowEntity extendFlow);

    /**
     * @param sequenceName
     * @return
     * @Description 获取序列
     */
    Long getSequence(@Param("sequenceName") String sequenceName);

    TransExtendFlowEntity findbyFlowIdForUpdate(String flowId);

    TransExtendFlowEntity findByChannelFlowId(@Param("channelFlowId") String channelFlowId);

    /**
     * 根据订单编号更新D0手续费和费率
     *
     * @param extendFlow 更新实体
     * @return 更新行数
     */
    int updateLklSmSyncByFlowId(TransExtendFlowEntity extendFlow);
}