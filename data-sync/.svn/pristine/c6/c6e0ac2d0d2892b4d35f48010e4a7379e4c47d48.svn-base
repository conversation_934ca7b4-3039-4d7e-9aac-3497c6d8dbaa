package com.pay.manage.sync.biz.lklsm;

import com.pay.frame.common.base.exception.OptimisticException;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.RandomUtils;
import com.pay.manage.sync.biz.SMSSendBiz;
import com.pay.manage.sync.mq.sync.LklSmTransFeeNotifyDto;
import com.pay.manage.sync.mq.sync.LklSmTransNotifyDto;
import com.pay.manage.sync.service.LklSmTransFeeSyncService;
import com.pay.manage.sync.service.LklSmTransSyncService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * LKLSM交易手续费数据同步业务处理
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
@Component
public class LklSmTransDataSyncBiz {

    @Autowired
    private LklSmTransSyncService lklSmTransSyncService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private SMSSendBiz sMSSendBiz;

    public void dataSyncImport(LklSmTransNotifyDto lklSmTransNotify) {
        RLock lock = null;
        boolean tryLock = false;
        try {
            // 使用trade_no作为锁的key，因为这是拉卡拉交易流水号
            lock = redissonClient.getLock("PC_TRANS_LKLSM_TOPIC_IMPORT_" + lklSmTransNotify.getTrade_no());
            tryLock = lock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!tryLock) {
                log.info("PC_TRANS_LKLSM_TOPIC 重复消费:{}", lklSmTransNotify.getTrade_no());
                throw new ServerException("PC_TRANS_LKLSM_TOPIC 重复消费");
            }
            lklSmTransSyncService.syncTrans(lklSmTransNotify);
        } catch (Exception e) {
            if (!(e instanceof OptimisticException)) {
                String fmt = "PC_TRANS_LKLSM_TOPIC_ERROR：trade_no: %s";
                String content = String.format(fmt, lklSmTransNotify.getTrade_no());
                sMSSendBiz.sendDingDingMsg("MS_TRANS_LKLSM_IN_" + RandomUtils.getFlowNo(), content);
                log.error("PC_TRANS_LKLSM_TOPIC error {}", lklSmTransNotify.getTrade_no(), e);
            }
            log.error("PC_TRANS_LKLSM_TOPIC error 【data:{}】", lklSmTransNotify, e);
            throw new ServerException("PC_TRANS_LKLSM_TOPIC error ：" + lklSmTransNotify.getTrade_no(), e);
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
    }
}
