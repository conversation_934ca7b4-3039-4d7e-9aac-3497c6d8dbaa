package com.pay.manage.sync.service;

import com.pay.frame.common.base.enums.TransStatus;
import com.pay.frame.common.base.enums.TransWay;
import com.pay.frame.common.base.exception.OptimisticException;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.manage.sync.entity.TransExtendFlowEntity;
import com.pay.manage.sync.mapper.TransExtendFlowSyncMapper;
import com.pay.manage.sync.mq.sync.LklSmTransFeeNotifyDto;
import com.pay.manage.sync.mq.sync.LklSmTransNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * LKLSM交易同步服务
 * 实现根据merchant_no查询客户信息，然后根据user_no查找绑定时间最早的POS信息，获取活动ID和min_id
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Slf4j
@Service
public class LklSmTransSyncService {

    @Resource
    private TransExtendFlowSyncMapper transExtendFlowSyncMapper;


    @Transactional(rollbackFor = Exception.class)
    public void syncTrans(LklSmTransNotifyDto transNotify) {
        if (transNotify == null) {
            throw new OptimisticException("PC_TRANS_LKLSM_TOPIC，数据同步异常，数据为空异常");
        }
        log.info("LKLSM交易状态同步处理: trade_no={}, merchant_no={}, out_trade_no={}, trade_status={}, total_amount={}",
                transNotify.getTrade_no(), transNotify.getMerchant_no(), transNotify.getOut_trade_no(),
                transNotify.getTrade_status(), transNotify.getTotal_amount());

        // 1. 根据out_trade_no查询是否已存在记录
        TransExtendFlowEntity transExtendFlow = transExtendFlowSyncMapper.findByChannelFlowId(transNotify.getOut_trade_no());
        if (transExtendFlow == null) {
            throw new OptimisticException("PC_TRANS_LKLSM_TOPIC，数据同步异常，交易记录不存在: " + transNotify.getOut_trade_no());
        }

        // 2. 更新交易状态和相关信息
        updateTransStatus(transNotify, transExtendFlow);
    }

    /**
     * 根据交易通知更新交易状态和相关信息
     *
     * @param transNotify 交易通知DTO，包含交易状态、金额等信息
     * @param transFlow   交易流水实体
     */
    public void updateTransStatus(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        // 1. 根据流水号查询交易记录并加锁
        TransExtendFlowEntity updateEntity = transExtendFlowSyncMapper.findbyFlowIdForUpdate(transFlow.getFlowId());
        if (updateEntity == null) {
            throw new OptimisticException("交易记录不存在: " + transFlow.getFlowId());
        }
        if (!updateEntity.getTransStatus().equals(TransStatus.INIT)) {
            log.info("交易状态更新失败，交易状态不是初始化不处理: flowId=" + updateEntity.getFlowId() + " trade_status=" + updateEntity.getTransStatus());
            return;
        }

        // 2. 更新交易状态和相关信息
        updateEntity.setTransStatus(mapTradeStatus(transNotify.getTrade_status()));
        updateEntity.setUpdateTime(new java.util.Date());

        // 处理用户标识信息
        if (StringUtils.hasText(transNotify.getUser_id2()) && "WECHAT".equals(transNotify.getAccount_type())) {
            transFlow.setOpenId(transNotify.getUser_id2());
        }

        int updateCount = transExtendFlowSyncMapper.updateLklSmSyncByFlowId(updateEntity);
        if (updateCount != 1) {
            throw new OptimisticException("交易状态更新失败，影响行数不为1: trade_no=" + transNotify.getTrade_no());
        }
    }


    /**
     * 映射拉卡拉交易状态到系统内部状态
     */
    private String mapTradeStatus(String lklStatus) {
        switch (lklStatus) {
            case "INIT":
                return "INIT";
            case "CREATE":
                return "CREATE";
            case "SUCCESS":
                return "SUCCESS";
            case "FAIL":
                return "FAIL";
            case "CLOSE":
                return "CLOSE";
            case "DEAL":
            case "UNKNOWN":
            case "PART_REFUND":
            case "REFUND":
            case "REVOKED":
            default:
                throw new ServerException("拉卡拉扫码状态异常");
        }
    }
}