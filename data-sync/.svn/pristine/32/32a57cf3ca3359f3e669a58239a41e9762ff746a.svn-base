package com.pay.manage.sync.mapper;


import com.pay.manage.sync.entity.CustomerEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 基础数据同步 Mapper
 *
 * <AUTHOR>
 */
public interface CustomerSyncMapper extends BaseSyncMapper<CustomerEntity> {

    CustomerEntity findByCustNo(@Param("customerNo") String customerNo);

    /**
     * 根据用户号查询客户信息
     * @param userNo 用户号
     * @return 客户信息
     */
    CustomerEntity findByUserNo(@Param("userNo") String userNo);
}
