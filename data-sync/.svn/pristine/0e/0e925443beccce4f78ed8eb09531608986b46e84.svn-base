package com.pay.manage.sync.mq.sync;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * LKLSM交易手续费同步DTO
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LklSmTransNotifyDto {

    /**
     * 商户号
     */
    private String merchant_no;
    /**
     * 商户交易流水号
     */
    private String out_trade_no;
    /**
     * 拉卡拉交易流水号
     */
    private String trade_no;
    /**
     * 拉卡拉对账单流水号 trade_no的后14位
     */
    private String log_no;
    /**
     * 账户端交易订单号
     */
    private String acc_trade_no;
    /**
     * 钱包类型 微信：WECHAT 支付宝：ALIPAY 银联：UQRCODEPAY 翼支付: BESTPAY 苏宁易付宝: SUNING  数字人民币-DCPAY
     */
    private String account_type;
    /**
     * 结算商户号
     */
    private String settle_merchant_no;
    /**
     * 账户端终端号
     */
    private String settle_term_no;
    /**
     * 交易状态 INIT-初始化 CREATE-下单成功 SUCCESS-交易成功 FAIL-交易失败 DEAL-交易处理中 UNKNOWN-未知状态 CLOSE-订单关闭 PART_REFUND-部分退款 REFUND-全部退款 REVOKED-订单撤销
     */
    private String trade_status;
    /**
     * 订单金额 单位分，整数数字型字符
     */
    private String total_amount;
    /**
     * 付款人实付金额 付款人实付金额，单位分
     */
    private String payer_amount;
    /**
     * 账户端结算金额 账户端应结订单金额，单位分 ，账户端应结订单金额=付款人实际发生金额+账户端优惠金额
     */
    private String acc_settle_amount;
    /**
     * 账户端优惠金额
     */
    private String acc_mdiscount_amount;
    /**
     * 账户端优惠金额
     */
    private String acc_discount_amount;
    /**
     * 账户端其他优惠金额
     */
    private String acc_other_discount_amount;
    /**
     * 交易完成时间 实际支付时间。yyyyMMddHHmmss
     */
    private String trade_time;
    /**
     * 用户标识1 微信sub_open_id, 支付宝buyer_logon_id（买家支付宝账号）
     */
    private String user_id1;
    /**
     * 用户标识2 微信openId,支付宝buyer_user_id,银联user_id
     */
    private String user_id2;
    /**
     * 账户端活动 ID 在账户端商户后台配置的批次 ID
     */
    private String acc_activity_id;
    /**
     * 付款银行 付款银行
     */
    private String bank_type;
    /**
     * 银行卡类型 00：借记 01：贷记 02：微信零钱 03：支付宝花呗 04：支付宝其他 05：数字货币 06：拉卡拉支付账户 99：未知 sha
     */
    private String card_type;
    /**
     * 备注
     */
    private String remark;
    /**
     * 花呗分期支付信息
     */
    private HbFqPayInfo hb_fq_pay_info;
    /**
     * 子商户号 账户端子商户号
     */
    private String sub_mch_id;
    /**
     * 合单信息
     */
    private List<OutSplitRspInfo> out_split_rsp_infos;
    /**
     * 优惠商品信息
     */
    private String discount_goods_detail;

    @Data
    public static class HbFqPayInfo {
        /**
         * 分期金额，单位分，整数数字型字符
         */
        private String fq_amount;
        /**
         * 分期期数，整数数字型字符
         */
        private String user_install_num;
    }

    @Data
    public static class OutSplitRspInfo {
        /**
         * 子单拉卡拉流水号
         */
        private String sub_trade_no;
        /**
         * 子单对账流水号
         */
        private String sub_log_no;

        /**
         * 子单外部流水号
         */
        private String out_sub_trade_no;

        /**
         * 子单商户号
         */
        private String merchant_no;

        /**
         * 子单终端号
         */
        private String term_no;

        /**
         * 子单金额，单位：分
         */
        private String amount;
    }

    @Data
    public static class DiscountGoodsDetail {
        /**
         * 商品id
         */
        private String goods_id;

        /**
         * 商品名称
         */
        private String goods_name;

        /**
         * 优惠金额
         */
        private String discount_amount;
        /**
         * 优惠id
         */
        private String voucher_id;
    }
}
