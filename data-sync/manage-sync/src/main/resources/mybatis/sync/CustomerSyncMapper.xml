<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.manage.sync.mapper.CustomerSyncMapper">
    <resultMap id="BaseResultMap" type="com.pay.manage.sync.entity.CustomerEntity">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="CUSTOMER_NO" jdbcType="VARCHAR" property="customerNo"/>
        <result column="USER_NO" jdbcType="VARCHAR" property="userNo"/>
        <result column="AGENT_NO" jdbcType="VARCHAR" property="agentNo"/>
        <result column="FULL_NAME" jdbcType="VARCHAR" property="fullName"/>
        <result column="SHORT_NAME" jdbcType="VARCHAR" property="shortName"/>
        <result column="ORGAN_CODE" jdbcType="VARCHAR" property="organCode"/>
        <result column="PHONE_NO" jdbcType="VARCHAR" property="phoneNo"/>
        <result column="ADDRESS" jdbcType="VARCHAR" property="address"/>
        <result column="SOURCE" jdbcType="VARCHAR" property="source"/>
        <result column="CUST_TYPE" jdbcType="VARCHAR" property="custType"/>
        <result column="MCC" jdbcType="VARCHAR" property="mcc"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="OPEN_TIME" jdbcType="TIMESTAMP" property="openTime"/>
        <result column="OPTIMISTIC_SYNC" jdbcType="DECIMAL" property="optimisticSync"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="ACT_TIME" jdbcType="TIMESTAMP" property="actTime"/>
        <result column="ACT_MID" jdbcType="VARCHAR" property="actMid"/>
        <result column="THIRD_CUST_NO" jdbcType="VARCHAR" property="thirdCustNo"/>
        <result column="BRAND" jdbcType="VARCHAR" property="brand"/>
        <result column="THIRD_TERM_NO" jdbcType="VARCHAR" property="thirdTermNo"/>
        <result column="CREDIT_CARD_NO" jdbcType="VARCHAR" property="creditCardNo"/>
        <result column="FACE_STATUS" jdbcType="VARCHAR" property="faceStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID
        , OPTIMISTIC, "STATUS", CUSTOMER_NO, USER_NO, AGENT_NO, FULL_NAME, SHORT_NAME, ORGAN_CODE,
    PHONE_NO, ADDRESS, "SOURCE", CUST_TYPE, MCC, CREATE_TIME, OPEN_TIME, OPTIMISTIC_SYNC,
    UPDATE_TIME, ACT_TIME, ACT_MID, THIRD_CUST_NO, BRAND, THIRD_TERM_NO, CREDIT_CARD_NO,
    FACE_STATUS
    </sql>

    <select id="findSyncData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.CUSTOMER
        where OPTIMISTIC > OPTIMISTIC_SYNC
        AND UPDATE_TIME > SYSDATE - 1 <![CDATA[and rownum <=#{dataSize}]]>
        ORDER BY ID
    </select>
    <select id="findByCustNo" resultType="com.pay.manage.sync.entity.CustomerEntity">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.CUSTOMER
        where CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
    </select>

    <select id="findByUserNo" resultType="com.pay.manage.sync.entity.CustomerEntity">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.CUSTOMER
        where USER_NO = #{userNo,jdbcType=VARCHAR}
    </select>

    <update id="updateSyncOpt" parameterType="com.pay.manage.sync.entity.CustomerEntity">
        update ZF_OPER_ADM.CUSTOMER
        set OPTIMISTIC_SYNC = #{optimistic,jdbcType=DECIMAL}
        where ID = #{id,jdbcType=DECIMAL}
          and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
    </update>
</mapper>