package com.pay.manage.sync.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 商户信息
 *
 * <AUTHOR>
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CustomerEntity extends BaseSyncEntity implements Serializable {

    /**
     * 商户状态
     */
    private String status;

    /**
     * 商户编号
     */
    private String customerNo;

    /**
     * 用户号
     */
    private String userNo;

    /**
     * 所属代理商编号
     */
    private String agentNo;

    /**
     * 商户全称
     */
    private String fullName;

    /**
     * 商户简称
     */
    private String shortName;

    /**
     * 所属地区
     */
    private String organCode;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 地址
     */
    private String address;

    /**
     * 来源
     */
    private String source;

    /**
     * 商户类型
     */
    private String custType;

    /**
     * 行业类别
     */
    private String mcc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 开通时间
     */
    private Date openTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 激活时间
     */
    private Date actTime;

    /**
     * 激活活动编号
     */
    private String actMid;

    /**
     * 三方编号
     */
    private String thirdCustNo;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 三方终端编号
     */
    private String thirdTermNo;

    /**
     * 贷记卡卡号
     */
    private String creditCardNo;

    /**
     * 活体状态
     */
    private String faceStatus;

    private static final long serialVersionUID = 1L;
}