package com.pay.manage.sync.service;

import com.pay.manage.sync.entity.CustomerEntity;
import com.pay.manage.sync.entity.PosEntity;
import com.pay.manage.sync.mapper.CustomerSyncMapper;
import com.pay.manage.sync.mapper.PosSyncMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 客户和POS信息查询服务
 * 
 * <AUTHOR>
 * @date 2025/7/31
 */
@Slf4j
@Service
public class CustomerPosQueryService {

    @Resource
    private CustomerSyncMapper customerSyncMapper;
    
    @Resource
    private PosSyncMapper posSyncMapper;

    /**
     * 根据商户号查询客户信息，然后根据user_no查找绑定时间最早的POS信息
     * 
     * @param merchantNo 商户号
     * @return POS信息中的活动ID和min_id信息
     */
    public PosActivityInfo queryPosActivityInfo(String merchantNo) {
        if (!StringUtils.hasText(merchantNo)) {
            log.warn("商户号为空，无法查询POS活动信息");
            return null;
        }

        try {
            // 1. 根据商户号查询客户信息
            CustomerEntity customer = customerSyncMapper.findByCustNo(merchantNo);
            if (customer == null) {
                log.warn("根据商户号未找到客户信息: {}", merchantNo);
                return null;
            }

            String userNo = customer.getUserNo();
            if (!StringUtils.hasText(userNo)) {
                log.warn("客户信息中user_no为空: merchantNo={}", merchantNo);
                // 如果没有userNo，直接使用merchantNo查询POS
                return queryPosByMerchantNo(merchantNo);
            }

            // 2. 根据user_no查询客户信息（可能有多个商户对应同一个user_no）
            CustomerEntity userCustomer = customerSyncMapper.findByUserNo(userNo);
            if (userCustomer == null) {
                log.warn("根据user_no未找到客户信息: userNo={}", userNo);
                return queryPosByMerchantNo(merchantNo);
            }

            // 3. 根据客户号查询绑定时间最早的POS信息
            return queryPosByMerchantNo(userCustomer.getCustomerNo());

        } catch (Exception e) {
            log.error("查询POS活动信息失败: merchantNo={}", merchantNo, e);
            return null;
        }
    }

    /**
     * 根据商户号查询绑定时间最早的POS信息
     */
    private PosActivityInfo queryPosByMerchantNo(String merchantNo) {
        try {
            // 查询绑定时间最早的POS信息
            PosEntity earliestPos = posSyncMapper.findEarliestBindPosByCustomerNo(merchantNo);
            if (earliestPos == null) {
                log.warn("未找到绑定的POS信息: merchantNo={}", merchantNo);
                return null;
            }

            log.info("找到绑定时间最早的POS信息: merchantNo={}, posSn={}, bindTime={}, actMid={}", 
                    merchantNo, earliestPos.getPosSn(), earliestPos.getBindTime(), earliestPos.getActMid());

            // 构建返回信息
            PosActivityInfo activityInfo = new PosActivityInfo();
            activityInfo.setMerchantNo(merchantNo);
            activityInfo.setPosSn(earliestPos.getPosSn());
            activityInfo.setBindTime(earliestPos.getBindTime());
            activityInfo.setActivityId(earliestPos.getActMid()); // 活动ID
            activityInfo.setMinId(earliestPos.getActMid()); // min_id，这里假设和活动ID相同
            activityInfo.setPolicyId(earliestPos.getActMid()); // 策略ID

            return activityInfo;

        } catch (Exception e) {
            log.error("根据商户号查询POS信息失败: merchantNo={}", merchantNo, e);
            return null;
        }
    }

    /**
     * POS活动信息
     */
    public static class PosActivityInfo {
        /**
         * 商户号
         */
        private String merchantNo;
        
        /**
         * POS序列号
         */
        private String posSn;
        
        /**
         * 绑定时间
         */
        private java.util.Date bindTime;
        
        /**
         * 活动ID
         */
        private String activityId;
        
        /**
         * min_id
         */
        private String minId;
        
        /**
         * 策略ID
         */
        private String policyId;

        // Getters and Setters
        public String getMerchantNo() {
            return merchantNo;
        }

        public void setMerchantNo(String merchantNo) {
            this.merchantNo = merchantNo;
        }

        public String getPosSn() {
            return posSn;
        }

        public void setPosSn(String posSn) {
            this.posSn = posSn;
        }

        public java.util.Date getBindTime() {
            return bindTime;
        }

        public void setBindTime(java.util.Date bindTime) {
            this.bindTime = bindTime;
        }

        public String getActivityId() {
            return activityId;
        }

        public void setActivityId(String activityId) {
            this.activityId = activityId;
        }

        public String getMinId() {
            return minId;
        }

        public void setMinId(String minId) {
            this.minId = minId;
        }

        public String getPolicyId() {
            return policyId;
        }

        public void setPolicyId(String policyId) {
            this.policyId = policyId;
        }

        @Override
        public String toString() {
            return "PosActivityInfo{" +
                    "merchantNo='" + merchantNo + '\'' +
                    ", posSn='" + posSn + '\'' +
                    ", bindTime=" + bindTime +
                    ", activityId='" + activityId + '\'' +
                    ", minId='" + minId + '\'' +
                    ", policyId='" + policyId + '\'' +
                    '}';
        }
    }
}
