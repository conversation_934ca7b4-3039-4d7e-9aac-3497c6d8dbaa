# 客户POS查询服务使用说明

## 概述

`CustomerPosQueryService` 是一个专门用于查询客户信息和POS活动信息的服务类。它实现了以下业务逻辑：

1. 根据 `merchant_no` 查询客户信息
2. 从客户信息中获取 `user_no`
3. 根据 `user_no` 查询对应的客户信息（可能与原始商户不同）
4. 查询该客户绑定时间最早的POS信息
5. 从POS信息中提取活动ID和min_id

## 核心业务流程

```mermaid
graph TD
    A[输入merchant_no] --> B[查询客户信息]
    B --> C{客户存在?}
    C -->|否| D[返回null]
    C -->|是| E{有user_no?}
    E -->|否| F[直接用merchant_no查询POS]
    E -->|是| G[根据user_no查询客户]
    G --> H{找到用户客户?}
    H -->|否| F
    H -->|是| I[用用户客户的merchant_no查询POS]
    F --> J[查询绑定时间最早的POS]
    I --> J
    J --> K{找到POS?}
    K -->|否| D
    K -->|是| L[返回POS活动信息]
```

## 使用方法

### 1. 在LklSmTransSyncService中的使用

```java
@Service
public class LklSmTransSyncService {
    
    @Resource
    private CustomerPosQueryService customerPosQueryService;
    
    public void syncTrans(LklSmTransNotifyDto transNotify) {
        // ... 其他逻辑
        
        // 查询客户信息和POS活动信息
        if (StringUtils.hasText(transNotify.getMerchant_no())) {
            CustomerPosQueryService.PosActivityInfo posActivityInfo = 
                customerPosQueryService.queryPosActivityInfo(transNotify.getMerchant_no());
            
            if (posActivityInfo != null) {
                log.info("查询到POS活动信息: {}", posActivityInfo);
                // 设置活动ID和策略ID
                transExtendFlow.setPolicyId(posActivityInfo.getPolicyId());
                // 如果有min_id字段，可以在这里设置
                // transExtendFlow.setMinId(posActivityInfo.getMinId());
            } else {
                log.warn("未查询到POS活动信息，使用通知中的活动ID: merchant_no={}, acc_activity_id={}", 
                        transNotify.getMerchant_no(), transNotify.getAcc_activity_id());
                // 如果查询不到POS信息，使用通知中的活动ID
                if (StringUtils.hasText(transNotify.getAcc_activity_id())) {
                    transExtendFlow.setPolicyId(transNotify.getAcc_activity_id());
                }
            }
        }
        
        // ... 其他逻辑
    }
}
```

### 2. 直接使用服务

```java
@Autowired
private CustomerPosQueryService customerPosQueryService;

public void someBusinessMethod() {
    String merchantNo = "MERCHANT_001";
    
    // 查询POS活动信息
    CustomerPosQueryService.PosActivityInfo posActivityInfo = 
        customerPosQueryService.queryPosActivityInfo(merchantNo);
    
    if (posActivityInfo != null) {
        System.out.println("商户号: " + posActivityInfo.getMerchantNo());
        System.out.println("POS序列号: " + posActivityInfo.getPosSn());
        System.out.println("绑定时间: " + posActivityInfo.getBindTime());
        System.out.println("活动ID: " + posActivityInfo.getActivityId());
        System.out.println("Min ID: " + posActivityInfo.getMinId());
        System.out.println("策略ID: " + posActivityInfo.getPolicyId());
    } else {
        System.out.println("未找到POS活动信息");
    }
}
```

## 返回结果说明

### PosActivityInfo 对象结构

```java
public static class PosActivityInfo {
    private String merchantNo;    // 商户号（可能与输入的不同）
    private String posSn;         // POS序列号
    private Date bindTime;        // 绑定时间
    private String activityId;    // 活动ID（来自POS的actMid字段）
    private String minId;         // min_id（当前实现中与activityId相同）
    private String policyId;      // 策略ID（当前实现中与activityId相同）
}
```

## 数据库表结构要求

### CUSTOMER表需要包含的字段

```sql
CREATE TABLE CUSTOMER (
    ID NUMBER PRIMARY KEY,
    CUSTOMER_NO VARCHAR2(32) NOT NULL,  -- 商户号
    USER_NO VARCHAR2(32),               -- 用户号（新增字段）
    FULL_NAME VARCHAR2(100),            -- 商户全称
    -- 其他字段...
);
```

### POS表需要包含的字段

```sql
CREATE TABLE POS (
    ID NUMBER PRIMARY KEY,
    POS_SN VARCHAR2(32) NOT NULL,       -- POS序列号
    CUSTOMER_NO VARCHAR2(32) NOT NULL,  -- 商户号
    BIND_TIME DATE,                     -- 绑定时间
    ACT_MID VARCHAR2(32),               -- 活动ID
    -- 其他字段...
);
```

## 异常处理

服务会在以下情况返回 `null`：

1. 输入的 `merchant_no` 为空或null
2. 根据 `merchant_no` 未找到客户信息
3. 根据 `user_no` 未找到对应的客户信息
4. 未找到绑定的POS信息

所有异常都会被捕获并记录日志，不会向上抛出。

## 性能考虑

1. **数据库索引**：建议在以下字段上创建索引
   - `CUSTOMER.CUSTOMER_NO`
   - `CUSTOMER.USER_NO`
   - `POS.CUSTOMER_NO`
   - `POS.BIND_TIME`

2. **查询优化**：
   - `findEarliestBindPosByCustomerNo` 查询使用了 `ORDER BY BIND_TIME ASC` 和 `ROWNUM = 1`
   - 避免了查询所有POS记录后在应用层排序

## 测试用例

参考 `CustomerPosQueryServiceTest` 类，包含以下测试场景：

1. **正常流程测试**：有user_no，能找到POS信息
2. **客户不存在测试**：merchant_no对应的客户不存在
3. **无user_no测试**：客户存在但没有user_no
4. **无POS测试**：客户存在但没有绑定POS
5. **空参数测试**：merchant_no为空或null
6. **不同用户客户测试**：user_no对应的客户与原始商户不同

## 日志记录

服务会记录以下关键信息：

- 查询到的POS活动信息（INFO级别）
- 未找到客户信息的警告（WARN级别）
- 未找到POS信息的警告（WARN级别）
- 查询过程中的异常（ERROR级别）

## 配置要求

确保以下配置正确：

1. **MyBatis配置**：CustomerSyncMapper.xml 和 PosSyncMapper.xml
2. **数据源配置**：确保能正确连接到数据库
3. **事务配置**：如果需要事务支持

## 扩展说明

如果需要扩展功能，可以考虑：

1. **缓存支持**：对频繁查询的客户信息进行缓存
2. **批量查询**：支持一次查询多个商户的POS信息
3. **更多POS信息**：返回更多POS相关的字段
4. **活动信息详情**：根据活动ID查询活动的详细信息

## 注意事项

1. **数据一致性**：确保CUSTOMER表中的USER_NO字段数据正确
2. **空值处理**：所有字段都可能为空，使用时需要进行空值检查
3. **时区问题**：BIND_TIME字段的时区处理
4. **并发安全**：服务是无状态的，支持并发调用

## 故障排查

### 常见问题

1. **返回null**：检查数据库中是否存在对应的客户和POS记录
2. **查询慢**：检查数据库索引是否正确创建
3. **数据不一致**：检查USER_NO字段是否正确维护

### 调试方法

1. 开启DEBUG日志查看详细的查询过程
2. 直接在数据库中执行SQL验证数据
3. 使用单元测试验证各种场景
