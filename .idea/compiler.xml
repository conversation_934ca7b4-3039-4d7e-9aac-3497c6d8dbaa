<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="paychannel" />
        <module name="account-common" />
        <module name="settle-core" />
        <module name="brand-channel-gateway" />
        <module name="pay-frame-common" />
        <module name="pay-portal" />
        <module name="brand-channel-core" />
        <module name="account-core" />
        <module name="manage-sync" />
        <module name="pay-frame-cache" />
        <module name="settle-fee" />
        <module name="trade-sync" />
      </profile>
    </annotationProcessing>
  </component>
</project>