<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pay-frame/pom.xml" />
        <option value="$PROJECT_DIR$/paychannel/pom.xml" />
        <option value="$PROJECT_DIR$/account/pom.xml" />
        <option value="$PROJECT_DIR$/account/account-core/pom.xml" />
        <option value="$PROJECT_DIR$/settle/pom.xml" />
        <option value="$PROJECT_DIR$/settle/settle-core/pom.xml" />
        <option value="$PROJECT_DIR$/data-sync/pom.xml" />
        <option value="$PROJECT_DIR$/pay-portal/pom.xml" />
        <option value="$PROJECT_DIR$/settle/settle-fee/pom.xml" />
        <option value="$PROJECT_DIR$/brand-channel/brand-channel-core/pom.xml" />
        <option value="$PROJECT_DIR$/brand-channel/brand-channel-gateway/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
  <component name="SvnBranchConfigurationManager">
    <option name="myConfigurationMap">
      <map>
        <entry key="$PROJECT_DIR$/account">
          <value>
            <SvnBranchConfiguration>
              <option name="trunkUrl" value="https://10.8.32.22/zfrepos/branch/********-LKL/account" />
            </SvnBranchConfiguration>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/brand-channel">
          <value>
            <SvnBranchConfiguration>
              <option name="trunkUrl" value="https://10.8.32.22/zfrepos/branch/********-LKL/brand-channel" />
            </SvnBranchConfiguration>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/data-sync">
          <value>
            <SvnBranchConfiguration>
              <option name="trunkUrl" value="https://10.8.32.22/zfrepos/branch/********-LKL/data-sync" />
            </SvnBranchConfiguration>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/pay-frame">
          <value>
            <SvnBranchConfiguration>
              <option name="trunkUrl" value="https://10.8.32.22/zfrepos/branch/********-LKL/pay-frame" />
            </SvnBranchConfiguration>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/pay-portal">
          <value>
            <SvnBranchConfiguration>
              <option name="branchUrls">
                <list>
                  <option value="https://10.8.32.22/zfrepos/branch" />
                  <option value="https://10.8.32.22/zfrepos/history" />
                  <option value="https://10.8.32.22/zfrepos/profile" />
                </list>
              </option>
              <option name="trunkUrl" value="https://10.8.32.22/zfrepos/trunk" />
            </SvnBranchConfiguration>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/paychannel">
          <value>
            <SvnBranchConfiguration>
              <option name="trunkUrl" value="https://10.8.32.22/zfrepos/branch/********-LKL/paychannel" />
            </SvnBranchConfiguration>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/settle">
          <value>
            <SvnBranchConfiguration>
              <option name="trunkUrl" value="https://10.8.32.22/zfrepos/branch/********-LKL/settle" />
            </SvnBranchConfiguration>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>