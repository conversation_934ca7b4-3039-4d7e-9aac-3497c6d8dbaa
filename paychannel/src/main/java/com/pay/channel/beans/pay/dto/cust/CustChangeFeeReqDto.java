package com.pay.channel.beans.pay.dto.cust;

import com.pay.channel.beans.pay.dto.FeeBean;
import com.pay.channel.beans.pay.dto.PayBaseReqDto;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;


/**
 * <AUTHOR> @date 2024/3/26
 * @apiNote
 */
@Data
@ToString(callSuper = true)
public class CustChangeFeeReqDto extends PayBaseReqDto {

    @NotEmpty(message = "通道商户编号不能为空")
    private String thirdCustNo;

    /**
     * 入网申请单号
     */
    private String incrFlowId;

    /**
     * 通道商户号
     */
    private String thirdCustId;


    @NotEmpty(message = "费率信息不能为空")
    private List<FeeBean> listFee;

}
