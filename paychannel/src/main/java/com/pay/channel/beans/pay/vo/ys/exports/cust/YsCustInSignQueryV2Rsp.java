package com.pay.channel.beans.pay.vo.ys.exports.cust;

import com.pay.channel.beans.pay.dto.custIncr.YsCustInSignQueryV2RspDto;
import com.pay.channel.beans.pay.dto.custIncr.YsCustInfoQueryResV2Dto;
import com.pay.channel.exception.ChannelPayException;
import com.pay.channel.utils.converters.ys.YsCustSignStatusConvUtil;
import com.pay.channel.utils.converters.ys.YsCustStatusConvUtil;
import com.pay.frame.common.base.enums.BillStatus;
import com.pay.frame.common.base.util.StringUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> @date 2025/7/31
 * @apiNote
 */
@Data
@ToString(callSuper = true)
public class YsCustInSignQueryV2Rsp {


    private String code;

    private String msg;

    /**
     * 业务响应码
     */
    private String subCode;

    /**
     * 业务响应描述
     */
    private String subMsg;

    /**
     * 业务响应参数集合
     */
    private YsCustInSignQueryV2BusDataRes businessData;



    @Getter
    @Setter
    public static class YsCustInSignQueryV2BusDataRes{
        /**
         * 状态,00-成功、01-初始化、02-签约中(电子合同才会出现此状态)、
         * 03-待审核(纸质合同或发起方不支持自动审核会出现此状态)、04-审核拒绝。
         */
        private String status;

        /**
         * 签约流水号,电子合同才有值
         */
        private String signId;

        /**
         * 权限流水号
         */
        private String authId;

        /**
         * 渠道商户号,成功时才有值
         */
        private String mercId;

        /**
         * 备注,审核拒绝才有值
         */
        private String note;

        /**
         * 业务方权限id
         */
        private String thirdAuthId;
    }




    public YsCustInSignQueryV2RspDto convRspDto() {
        if(!"00000".equals(code)){
            throw new ChannelPayException(code, msg);
        }
        if (!"0000".equals(subCode)) {
            String message = StringUtils.isNotBlank(subMsg) ? subMsg : msg;
            throw new ChannelPayException(subCode, message);
        }

        YsCustInSignQueryV2RspDto rsp = new YsCustInSignQueryV2RspDto();
        YsCustInSignQueryV2BusDataRes dataRes = this.getBusinessData();
        rsp.setStatus(YsCustSignStatusConvUtil.code2SignStatus(dataRes.getStatus()));
        rsp.setSignId(dataRes.getSignId());
        rsp.setAuthId(dataRes.getAuthId());
        rsp.setMercId(dataRes.getMercId());
        rsp.setNote(dataRes.getNote());
        rsp.setThirdAuthId(dataRes.getThirdAuthId());

        return rsp;
    }


}
