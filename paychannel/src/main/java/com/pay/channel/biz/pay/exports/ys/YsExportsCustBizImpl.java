package com.pay.channel.biz.pay.exports.ys;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pay.channel.beans.pay.dto.FeeBean;
import com.pay.channel.beans.pay.dto.cust.*;
import com.pay.channel.beans.pay.dto.custIncr.YsCustInfoSignAndMdyFeeReqV2Dto;
import com.pay.channel.beans.pay.vo.ys.exports.cust.*;
import com.pay.channel.biz.pay.exports.PayCustomerBiz;
import com.pay.channel.configuration.ApiConfig;
import com.pay.channel.exception.ChannelPayException;
import com.pay.channel.utils.ValidationUtils;
import com.pay.frame.common.base.enums.CardType;
import com.pay.frame.common.base.enums.Origin;
import com.pay.frame.common.base.enums.TransWay;
import com.pay.frame.common.base.util.FmtNumber;
import com.pay.frame.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR> @date 2024/11/5
 * @apiNote
 */
@Slf4j
@Component
public class YsExportsCustBizImpl implements PayCustomerBiz {

    @Resource
    private YsPayExportsBiz ysPayExportsBiz;

    @Resource
    private ApiConfig apiConfig;


    @Override
    public Origin getCode() {
        return Origin.YS;
    }


    /**
     * 银行卡变更
     *
     * @param reqDto
     * @return
     */
    @Override
    public CustChangeSettleCardRspDto changeSettleCard(CustChangeSettleCardReqDto reqDto) {
        YsCustChangeSettleCardV2Req reqV2 = YsCustChangeSettleCardV2Req.convChangeSettleCardReq(reqDto);
        YsCustChangeSettleCardV2Rsp rsp = ysPayExportsBiz.biz(apiConfig.getYsChangeSettleCard(), reqV2, "商户结算卡变更", new TypeReference<YsCustChangeSettleCardV2Rsp>() {
        });

        CustChangeSettleCardRspDto rspDto = rsp.convChangeSettleCardRspDto();
        log.info("ys customer change settle card end【{}】, 【req: {}, rsp: {}】", reqDto, reqV2, rsp);
        return rspDto;

//
//        YsCustChangeSettleCardReq req = YsCustChangeSettleCardReq.convChangeSettleCardReq(reqDto);
//        ValidationUtils.validateObject(req);
//        ValidationUtils.validateObject(req.getMercSettleAccountParam());
////        if (YesNo.N.name().equals(req.getMercSettleAccountParam().getAccountNameType())) {
////            ValidationUtils.validateObject(req.getLegelSettleAccountParam());
////        }
//
//        YsCustChangeSettleCardRsp rsp = ysPayExportsBiz.biz(apiConfig.getYsChangeSettleCard(), req,
//                           "商户结算卡变更", new TypeReference<YsCustChangeSettleCardRsp>() {});
//        CustChangeSettleCardRspDto rspDto = rsp.convChangeSettleCardRspDto();
//        log.info("ys customer change settle card end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
//        return rspDto;

    }


    /**
     * 查询已注册品牌商
     *
     * @param reqDto
     * @return
     */
    @Override
    public CustIdCardQueryRspDto custIdCardQuery(CustIdCardQueryReqDto reqDto) {
        YsCustIdCardQueryReq req = YsCustIdCardQueryReq.convReq(reqDto);
        ValidationUtils.validateObject(req);
        if ("1".equals(req.getQueryType()) && StringUtils.isEmpty(req.getLicNo())) {
            throw new ChannelPayException("查询时营业执照号不能为空");
        } else {
            if (StringUtils.isEmpty(req.getArtifNm()) || StringUtils.isEmpty(req.getArtifCertifId())) {
                throw new ChannelPayException("查询时法人姓名或身份证不能为空");
            }
        }

        YsCustIdCardQueryRsp rsp = ysPayExportsBiz.biz(apiConfig.getYsCustIdCardQuery(), req, "商户预注册查询", new TypeReference<YsCustIdCardQueryRsp>() {
        });
        CustIdCardQueryRspDto rspDto = rsp.convRspDto();
        log.info("ys customer idCard query end【{}】, 【req: {}, rsp: {}】, 【rsp dto: {}】", reqDto, req, rsp, rspDto);
        return rspDto;
    }

    /**
     * 双录认证资质上送
     *
     * @param reqDto
     * @return
     */
    @Override
    public UploadSlFlagRspDto uploadSlFlag(UploadSlFlagReqDto reqDto) {
        YsUploadSlFlagReq req = YsUploadSlFlagReq.convReq(reqDto);
        ValidationUtils.validateObject(req);

        YsUploadSlFlagRsp rsp = ysPayExportsBiz.biz(apiConfig.getLsOrYsUploadSlFlag(), req, "上传双录资料", new TypeReference<YsUploadSlFlagRsp>() {
        });
        UploadSlFlagRspDto rspDto = new UploadSlFlagRspDto();
        log.info("ys customer upload sl flag end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;
    }

    /**
     * 商户费率变更
     * @param reqDto
     * @return
     */
    @Override
    public CustChangeFeeRspDto changeFee(CustChangeFeeReqDto reqDto) {

        YsCustInfoSignAndMdyFeeReqV2Dto.CodeScanT1Fee codeScanT1Fee = new YsCustInfoSignAndMdyFeeReqV2Dto.CodeScanT1Fee();
        YsCustInfoSignAndMdyFeeReqV2Dto.SwCardT1Fee swCardT1Fee = new YsCustInfoSignAndMdyFeeReqV2Dto.SwCardT1Fee();
        for (FeeBean fee : reqDto.getListFee()) {

            if (TransWay.WX == fee.getTransWay()) {
                YsCustInfoSignAndMdyFeeReqV2Dto.WxFee wxFee = new YsCustInfoSignAndMdyFeeReqV2Dto.WxFee();
                wxFee.setRateType("0");
                wxFee.setRateFee(FmtNumber.multiply100(fee.getRate()).toString());
                wxFee.setRateBottom("1");
                codeScanT1Fee.setWxPayFee(wxFee);
            }

            if (TransWay.ZFB == fee.getTransWay()) {
                YsCustInfoSignAndMdyFeeReqV2Dto.AliPayFee aliPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.AliPayFee();
                aliPayFee.setRateType("0");
                aliPayFee.setRateFee(FmtNumber.multiply100(fee.getRate()).toString());
                codeScanT1Fee.setAliPayFee(aliPayFee);

                Optional<FeeBean> first = reqDto.getListFee().stream().filter(x -> x.getTransWay() == TransWay.YHK && x.getCardType() == CardType.DEBIT_CARD).findFirst();
                if (first.isPresent()) {
                    YsCustInfoSignAndMdyFeeReqV2Dto.AliPayDebitFee aliPayDebitFee = new YsCustInfoSignAndMdyFeeReqV2Dto.AliPayDebitFee();
                    aliPayDebitFee.setRateType("0");
                    aliPayDebitFee.setRateFee(FmtNumber.multiply100(first.get().getRate()).toString());
                    aliPayDebitFee.setRateBottom("1");
                    codeScanT1Fee.setAliPayDebitFee(aliPayDebitFee);
                }
                Optional<FeeBean> first1 = reqDto.getListFee().stream().filter(x -> x.getTransWay() == TransWay.YHK && x.getCardType() == CardType.CREDIT_CARD).findFirst();
                if (first1.isPresent()) {
                    YsCustInfoSignAndMdyFeeReqV2Dto.AliPayCreditFee aliPayCreditFee = new YsCustInfoSignAndMdyFeeReqV2Dto.AliPayCreditFee();
                    aliPayCreditFee.setRateType("0");
                    aliPayCreditFee.setRateFee(FmtNumber.multiply100(first1.get().getRate()).toString());
                    aliPayCreditFee.setRateBottom("1");
                    codeScanT1Fee.setAliPayCreditFee(aliPayCreditFee);
                }
            }

            if (TransWay.YL == fee.getTransWay()) {
                Optional<FeeBean> first = reqDto.getListFee().stream().filter(x -> x.getTransWay() == TransWay.YHK && x.getCardType() == CardType.DEBIT_CARD).findFirst();
                if (first.isPresent()) {
                    YsCustInfoSignAndMdyFeeReqV2Dto.Bank1DebitPayFee bank1debitPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.Bank1DebitPayFee();
                    bank1debitPayFee.setRateType("0");
                    bank1debitPayFee.setRateFee(FmtNumber.multiply100(fee.getRate()).toString());
                    bank1debitPayFee.setRateBottom("1");
                    codeScanT1Fee.setBank1debitPayFee(bank1debitPayFee);

                    YsCustInfoSignAndMdyFeeReqV2Dto.Bank2DebitPayFee bank2debitPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.Bank2DebitPayFee();
                    bank2debitPayFee.setRateType("0");
                    bank2debitPayFee.setRateFee(FmtNumber.multiply100(fee.getRate()).toString());
                    bank2debitPayFee.setRateBottom("1");
                    codeScanT1Fee.setBank2debitPayFee(bank2debitPayFee);
                }

                Optional<FeeBean> first1 = reqDto.getListFee().stream().filter(x -> x.getTransWay() == TransWay.YHK && x.getCardType() == CardType.CREDIT_CARD).findFirst();
                if (first1.isPresent()) {
                    YsCustInfoSignAndMdyFeeReqV2Dto.Bank1CreditPayFee bank1CreditPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.Bank1CreditPayFee();
                    bank1CreditPayFee.setRateType("0");
                    bank1CreditPayFee.setRateFee(FmtNumber.multiply100(fee.getRate()).toString());
                    bank1CreditPayFee.setRateBottom("1");
                    codeScanT1Fee.setBank1creditPayFee(bank1CreditPayFee);

                    YsCustInfoSignAndMdyFeeReqV2Dto.Bank2CreditPayFee bank2CreditPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.Bank2CreditPayFee();
                    bank2CreditPayFee.setRateType("0");
                    bank2CreditPayFee.setRateFee(FmtNumber.multiply100(fee.getRate()).toString());
                    bank2CreditPayFee.setRateBottom("10");
                    codeScanT1Fee.setBank2creditPayFee(bank2CreditPayFee);
                }
            }

            if (TransWay.YHK == fee.getTransWay()) {
                if (CardType.DEBIT_CARD == fee.getCardType()) {
                    YsCustInfoSignAndMdyFeeReqV2Dto.DebitPayFee debitPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.DebitPayFee();
                    debitPayFee.setRateType("0");
                    debitPayFee.setRateFee(FmtNumber.multiply100(fee.getRate()).toString());
                    debitPayFee.setRateBottom("1");
                    debitPayFee.setRateTop("10000");
                    swCardT1Fee.setDebitPayFee(debitPayFee);
                }
                if (CardType.CREDIT_CARD == fee.getCardType()) {
                    YsCustInfoSignAndMdyFeeReqV2Dto.CreditPayFee creditPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.CreditPayFee();
                    creditPayFee.setRateType("0");
                    creditPayFee.setRateFee(FmtNumber.multiply100(fee.getRate()).toString());
                    creditPayFee.setRateBottom("1");
                    swCardT1Fee.setCreditPayFee(creditPayFee);
                }
            }
        }

        YsCustInfoSignAndMdyFeeReqV2Dto reqV2Dto = new YsCustInfoSignAndMdyFeeReqV2Dto();
        reqV2Dto.setCodeScanT1Fee(codeScanT1Fee);
        reqV2Dto.setSwCardT1Fee(swCardT1Fee);
        reqV2Dto.setCustomerNo(reqDto.getThirdCustNo());
        reqV2Dto.setCustId(reqDto.getThirdCustId());

        YsCustInfoSignReq req = YsCustInfoSignReq.convReq(reqV2Dto);

        String interType = apiConfig.getYsChangeFeeV2();
        if (StringUtils.isNotBlank(reqDto.getThirdCustId())) {
            interType = apiConfig.getYsCustInfoSign();
        }
        YsCustInfoSignRes rsp = ysPayExportsBiz.biz(interType, req, "银盛商户费率修改",
                new TypeReference<YsCustInfoSignRes>() {
        });
        CustChangeFeeRspDto rspDto = rsp.convFeeRspDto();
        log.info("ys customer info sign end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;

//        StringBuilder message = new StringBuilder();
//        StringBuilder channelMessage = new StringBuilder();
//
//        for (FeeBean feeBean : reqDto.getListFee()) {
//
//            if (TransWay.YHK.name().equals(feeBean.getTransWay().name())
//                    || TransWay.WX.name().equals(feeBean.getTransWay().name())
//                    || TransWay.ZFB.name().equals(feeBean.getTransWay().name())
//                    || TransWay.YL.name().equals(feeBean.getTransWay().name()) ) {
//
//                YsCustChangeFeeReq req = YsCustChangeFeeReq.convFeeReq(feeBean);
//                req.setCustomerNo(reqDto.getThirdCustNo());
//
//                try {
//                    YsCustChangeFeeRsp rsp = ysPayExportsBiz.biz(apiConfig.getYsChangeFee(), req,
//                            "", new TypeReference<YsCustChangeFeeRsp>() {});
//                }catch (Exception e){
//                    String cardType = CardType.DEFAULT.name().equals(feeBean.getCardType().name()) ? "" : feeBean.getCardType().getName();
//                    String str = feeBean.getTransWay().getMsg() + cardType + "费率变更未成功,";
//                    message.append(str);
//
//                    channelMessage.append(e.getMessage()+",");
//                }
//
//            }
//        }
//
//
//        String channelStatus = ChannelPosCustStatus.SUCCESS.name();
//        if (StringUtils.isNotBlank(message.toString())) {
//            message.setLength(message.length() - 1);
//        }
//        if (StringUtils.isNotBlank(channelMessage.toString())) {
//            channelMessage.setLength(channelMessage.length() - 1);
//            channelStatus = ChannelPosCustStatus.FAIL.name();
//        }
//
//        CustChangeFeeRspDto rspDto = new CustChangeFeeRspDto();
//        rspDto.setChannelStatus(channelStatus);
//        rspDto.setMessage(message.toString());
//        rspDto.setChannelMessage(channelMessage.toString());
//        log.info("ys customer change fee end【{}】, 【req: {}, rspDto: {}】", reqDto, rspDto);
//        return rspDto;
    }


}
