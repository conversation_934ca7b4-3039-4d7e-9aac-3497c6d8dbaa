package com.pay.channel.biz.pay.exports.ys;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pay.channel.beans.pay.dto.cust.CustChangeFeeReqDto;
import com.pay.channel.beans.pay.dto.cust.CustChangeFeeRspDto;
import com.pay.channel.beans.pay.dto.custIncr.*;
import com.pay.channel.beans.pay.vo.ys.exports.cust.*;
import com.pay.channel.configuration.ApiConfig;
import com.pay.channel.exception.ChannelPayException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR> @date 2024/11/15
 * @apiNote
 */
@Slf4j
@Component
public class YsExportsCustIncrBizImpl {

    @Resource
    private YsPayExportsBiz ysPayExportsBiz;

    @Resource
    private YsExportsCustBizImpl ysExportsCustBiz;

    @Resource
    private ApiConfig apiConfig;


    /**
     * 银盛商户信息入网
     * @param reqDto
     * @return
     */
    public YsCustIncrRspDto ysCustInfoIncr(YsCustIncrReqDto reqDto) {
        YsCustIncrReq req = YsCustIncrReq.convReq(reqDto);

        String interType = apiConfig.getYsCustInfoIncr();
        if (!StringUtils.isEmpty(req.getCustomerNo())) {
            interType = apiConfig.getYsMdyCustInfoIncr();
        }
        YsCustIncrRsp rsp = ysPayExportsBiz.biz(interType, req, "商户信息入网",
                new TypeReference<YsCustIncrRsp>() {
                });
        YsCustIncrRspDto rspDto = rsp.convRspDto();
        log.info("ys customer info incr end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;
    }

    /**
     * 银盛商户入网 V2
     *
     * @param reqDto
     * @return
     */
    public YsCustIncrRspV2Dto ysCustInfoIncrV2(YsCustIncrReqV2Dto reqDto) {
        YsCustIncrV2Req req = YsCustIncrV2Req.convReq(reqDto);

        String interType = apiConfig.getYsCustInfoIncrV2();
        if (!StringUtils.isEmpty(req.getSysFlowId())) {
            interType = apiConfig.getYsMdyCustInfoIncrV2();
        }
        YsCustIncrV2Rsp rsp = ysPayExportsBiz.biz(interType, req, "银盛商户入网V2",
                new TypeReference<YsCustIncrV2Rsp>() {
                });
        YsCustIncrRspV2Dto rspDto = rsp.convRspDto();
        log.info("ys 银盛商户信息入网V2 end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;
    }


    /**
     * 银盛商户结算卡入网
     * @param reqDto
     * @return
     */
    public YsCustSettleAccIncrRspDto ysCustSettleAccIncr(YsCustSettleAccIncrReqDto reqDto) {
        YsCustSettleAccIncrReq req = YsCustSettleAccIncrReq.convReq(reqDto);

        YsCustSettleAccIncrRsp rsp = ysPayExportsBiz.biz(apiConfig.getYsCustSettleAccIncr(), req,
                "商户结算卡入网", new TypeReference<YsCustSettleAccIncrRsp>() {
                });
        YsCustSettleAccIncrRspDto rspDto = rsp.convRspDto();
        log.info("ys customer settle account incr end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;
    }

    /**
     * 银盛商户图片上送
     *
     * @param reqDto
     */
    public void ysCustFileIncr(YsCustInfoFileReqDto reqDto) {
        YsCustInfoFileReq req = YsCustInfoFileReq.convReq(reqDto);
        YsCustInfoFileRes rsp = ysPayExportsBiz.biz(apiConfig.getYsCustFileIncr(), req,
                "银盛商户入网V2 图片资料上送", new TypeReference<YsCustInfoFileRes>() {
                });
        log.info("ys customer file info incr end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        if (!"0000".equals(rsp.getSubCode())) {
            throw new ChannelPayException(rsp.getSubCode(), rsp.getSubMsg());
        }
    }

    /**
     * 银盛商户资料确认
     * @param reqDto
     * @return
     */
    public YsCustInfoAuditResV2Dto ysCustInAudit(YsCustInfoAuditReqV2Dto reqDto) {
        YsCustInfoAuditReq req = YsCustInfoAuditReq.convReq(reqDto);
        YsCustInfoAuditRes rsp = ysPayExportsBiz.biz(apiConfig.getYsCustInAudit(), req,
                "银盛商户入网V2 资料确认", new TypeReference<YsCustInfoAuditRes>() {
                });
        YsCustInfoAuditResV2Dto rspDto = rsp.convRspDto();
        log.info("ys customer info incr end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;
    }

    /**
     * 银盛商户信息查询
     * @param reqDto
     * @return
     */
    public YsCustInfoQueryResV2Dto ysCustIncrQueryV2(YsCustInfoQueryReqV2Dto reqDto) {
        YsCustInfoQueryReq req = YsCustInfoQueryReq.convReq(reqDto);
        YsCustInfoQueryRes rsp = ysPayExportsBiz.biz(apiConfig.getYsCustInfoQuery(), req,
                "银盛商户信息查询", new TypeReference<YsCustInfoQueryRes>() {
                });
        YsCustInfoQueryResV2Dto rspDto = rsp.convRspDto();
        log.info("ys customer info incr end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;
    }

    /**
     * 银盛商户合同申请
     * @param reqDto
     * @return
     */
    public CustChangeFeeRspDto ysCustInSignV2(CustChangeFeeReqDto reqDto) {
        CustChangeFeeRspDto rspDto = ysExportsCustBiz.changeFee(reqDto);
        log.info("ys customer info sign end 【 req: {}, rsp: {}】", reqDto, rspDto);
        return rspDto;
    }


    /**
     * 银盛商户签约合同查询
     * @param reqV2
     * @return
     */
    public YsCustInSignQueryV2RspDto ysCustInSignQueryV2(YsCustInSignQueryV2Req reqV2) {
        YsCustInSignQueryV2Rsp rsp = ysPayExportsBiz.biz(apiConfig.getYsCustInfoSignQuery(), reqV2,
                "银盛商户签约合同查询", new TypeReference<YsCustInSignQueryV2Rsp>() {
                });
        YsCustInSignQueryV2RspDto rspDto = rsp.convRspDto();
        log.info("ys customer info incr end【{}】, 【req: {}, rsp: {}, rspDto:{}】", reqV2, rsp, rspDto);
        return rspDto;
    }


    /**
     * 银盛商户费率入网
     *
     * @param reqDto
     * @return
     */
    public YsCustFeeIncrRspDto ysCustFeeIncr(YsCustFeeIncrReqDto reqDto) {
        YsCustFeeIncrReq req = YsCustFeeIncrReq.convReq(reqDto);

        YsCustFeeIncrRsp rsp = ysPayExportsBiz.biz(apiConfig.getYsCustFeeIncr(), req,
                "商户费率入网", new TypeReference<YsCustFeeIncrRsp>() {
                });
        YsCustFeeIncrRspDto rspDto = rsp.convRspDto();
        log.info("ys customer fee incr end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;
    }

    /**
     * 商户收单进件查询接口
     *
     * @param reqDto
     * @return
     */
    public YsCustIncrQueryRspDto incrQuery(CustIncrQueryReqDto reqDto) {
        YsCustIncrQueryReq req = YsCustIncrQueryReq.convReq(reqDto);
        if (StringUtils.isEmpty(req.getCustomerNo()) && StringUtils.isEmpty(req.getOutCustomerNo())) {
            throw new ChannelPayException("查询商户信息参数不能为空");
        }

        YsCustIncrQueryRsp rsp = ysPayExportsBiz.biz(apiConfig.getYsIncrQuery(), req,
                "商户收单进件查询", new TypeReference<YsCustIncrQueryRsp>() {
                });
        YsCustIncrQueryRspDto rspDto = rsp.convRspDto();
        log.info("ys customer incr query end【{}】, 【req: {}, rsp: {}】", reqDto, req, rsp);
        return rspDto;
    }

    /**
     * MCC查询-1294
     *
     * @param mccCd    MCC码
     * @param mercType 商户类型
     * @return 结果结构体
     */
    public YsMccQueryRspDto queryMcc(String mccCd, String mercType) {
        if ((mccCd == null || mccCd.isEmpty()) && (mercType == null || mercType.isEmpty())) {
            throw new ChannelPayException("mccCd和mercType不能同时为空");
        }
        YsMccQueryReq req = new YsMccQueryReq();
        req.setMccCd(mccCd);
        req.setMercType(mercType);
        YsMccQueryRspDto ysResult = ysPayExportsBiz.biz(apiConfig.getYsMccQuery(), req, "银盛MCC查询",
                new TypeReference<YsMccQueryRspDto>() {
                });
        return ysResult;
    }

    /**
     * 地区信息查询-1296
     *
     * @param reqDto 查询参数
     * @return 结果结构体
     */
    public YsAreaQueryRspDto queryArea(YsAreaQueryReqDto reqDto) {
        YsAreaQueryReq req = new YsAreaQueryReq();
        req.setParentCityCd(reqDto.getParentCityCd());
        req.setAreaLevel(reqDto.getAreaLevel());
        req.setPageNumber(reqDto.getPageNumber());
        req.setPageSize(reqDto.getPageSize());
        req.setCityCd(reqDto.getCityCd());
        req.setCityName(reqDto.getCityName());
        YsAreaQueryRspDto result = ysPayExportsBiz.biz(apiConfig.getYsAreaQuery(), req, "银盛地区信息查询",
                new TypeReference<YsAreaQueryRspDto>() {
                });
        return result;
    }


}
