package com.pay.channel.cloud.pay.exports;

import com.pay.channel.beans.pay.dto.cust.CustChangeFeeReqDto;
import com.pay.channel.beans.pay.dto.cust.CustChangeFeeRspDto;
import com.pay.channel.beans.pay.dto.custIncr.*;
import com.pay.channel.beans.pay.vo.ys.exports.cust.YsCustInSignQueryV2Req;
import com.pay.channel.beans.pay.vo.ys.exports.cust.YsCustInSignQueryV2Rsp;
import com.pay.channel.biz.pay.exports.ls.LsExportsCustIncrBizImpl;
import com.pay.channel.biz.pay.exports.ys.YsExportsCustIncrBizImpl;
import com.pay.channel.biz.pay.exports.zf.ZfExportsCustIncrBizImpl;
import com.pay.channel.entity.AreaInfo;
import com.pay.channel.service.AreaInfoService;
import com.pay.frame.common.base.bean.ResultsBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> @date 2024/11/14
 * @apiNote
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/exports/custIncr")
public class PayCustIncrProvider {

    @Resource
    private ZfExportsCustIncrBizImpl zfCustIncrBizImpl;

    @Resource
    private LsExportsCustIncrBizImpl lsCustIncrBizImpl;

    @Resource
    private YsExportsCustIncrBizImpl ysCustIncrBizImpl;

    @Resource
    private AreaInfoService areaInfoService;

    /**
     * 中付商户注册
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/zfCustIncr", method = RequestMethod.POST)
    public ResultsBean<ZfCustIncrRspDto> zfCustIncr(@RequestBody @Validated ZfCustIncrReqDto reqDto) {
        ZfCustIncrRspDto rspDto = zfCustIncrBizImpl.custIncr(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 中付商户注册查询
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/zfIncrQuery", method = RequestMethod.POST)
    public ResultsBean<ZfCustIncrQueryRspDto> zfIncrQuery(@RequestBody @Validated CustIncrQueryReqDto reqDto) {
        ZfCustIncrQueryRspDto rspDto = zfCustIncrBizImpl.incrQuery(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }


    /**
     * 乐刷商户入网
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/lsCustIncr", method = RequestMethod.POST)
    public ResultsBean<LsCustIncrRspDto> lsCustIncr(@RequestBody @Validated LsCustIncrReqDto reqDto) {
        LsCustIncrRspDto rspDto = lsCustIncrBizImpl.lsCustIncr(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 乐刷 商户收单进件查询接口
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/lsIncrQuery", method = RequestMethod.POST)
    public ResultsBean<LsCustIncrQueryRspDto> lsIncrQuery(@RequestBody @Validated CustIncrQueryReqDto reqDto) {
        LsCustIncrQueryRspDto rspDto = lsCustIncrBizImpl.incrQuery(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }


    /**
     * 银盛商户信息入网
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/ysCustInfoIncr", method = RequestMethod.POST)
    public ResultsBean<YsCustIncrRspDto> ysCustInfoIncr(@RequestBody @Validated YsCustIncrReqDto reqDto) {
        YsCustIncrRspDto rspDto = ysCustIncrBizImpl.ysCustInfoIncr(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 银盛商户信息入网V2
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/ysCustInfoIncrV2", method = RequestMethod.POST)
    public ResultsBean<YsCustIncrRspV2Dto> ysCustInfoIncrV2(@RequestBody @Validated YsCustIncrReqV2Dto reqDto) {
        YsCustIncrRspV2Dto rspDto = ysCustIncrBizImpl.ysCustInfoIncrV2(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 银盛商户结算卡入网
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/ysCustSettleAccIncr", method = RequestMethod.POST)
    public ResultsBean<YsCustSettleAccIncrRspDto> ysCustSettleAccIncr(@RequestBody @Validated YsCustSettleAccIncrReqDto reqDto) {
        YsCustSettleAccIncrRspDto rspDto = ysCustIncrBizImpl.ysCustSettleAccIncr(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 银盛图片资料上送
     *
     * @param reqDto
     * @return
     */
    @PostMapping(value = "/ysCustFileIncr")
    public ResultsBean<String> ysCustFileIncr(@RequestBody @Validated YsCustInfoFileReqDto reqDto) {
        ysCustIncrBizImpl.ysCustFileIncr(reqDto);
        return ResultsBean.SUCCESS();
    }

    /**
     * 银盛商户资料确认
     *
     * @param reqDto
     * @return
     */
    @PostMapping(value = "/ysCustInAudit")
    public ResultsBean<YsCustInfoAuditResV2Dto> ysCustInAudit(@RequestBody @Validated YsCustInfoAuditReqV2Dto reqDto) {
        YsCustInfoAuditResV2Dto resV2Dto = ysCustIncrBizImpl.ysCustInAudit(reqDto);
        return ResultsBean.SUCCESS(resV2Dto);
    }

    /**
     * 银盛商户费率入网
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/ysCustFeeIncr", method = RequestMethod.POST)
    public ResultsBean<YsCustFeeIncrRspDto> ysCustFeeIncr(@RequestBody @Validated YsCustFeeIncrReqDto reqDto) {
        YsCustFeeIncrRspDto rspDto = ysCustIncrBizImpl.ysCustFeeIncr(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 银盛商户合同申请
     *
     * @param reqV2Dto
     * @return
     */
    @PostMapping(value = "/ysCustInSignV2")
    public ResultsBean<CustChangeFeeRspDto> ysCustInSignV2(@RequestBody @Validated CustChangeFeeReqDto reqV2Dto) {
        CustChangeFeeRspDto rspDto = ysCustIncrBizImpl.ysCustInSignV2(reqV2Dto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 银盛商户合同申请
     * @param reqV2Dto
     * @return
     */
    @PostMapping(value = "/ysCustInSignQueryV2")
    public ResultsBean<YsCustInSignQueryV2RspDto> ysCustInSignQueryV2(@RequestBody @Validated YsCustInSignQueryV2Req reqV2Dto) {
        YsCustInSignQueryV2RspDto rspDto = ysCustIncrBizImpl.ysCustInSignQueryV2(reqV2Dto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 银盛 商户收单进件查询接口
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/ysIncrQuery", method = RequestMethod.POST)
    public ResultsBean<YsCustIncrQueryRspDto> ysIncrQuery(@RequestBody @Validated CustIncrQueryReqDto reqDto) {
        YsCustIncrQueryRspDto rspDto = ysCustIncrBizImpl.incrQuery(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * 银盛商户信息查询
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = "/ysCustIncrQueryV2", method = RequestMethod.POST)
    public ResultsBean<YsCustInfoQueryResV2Dto> ysCustIncrQueryV2(@RequestBody @Validated YsCustInfoQueryReqV2Dto reqDto) {
        YsCustInfoQueryResV2Dto rspDto = ysCustIncrBizImpl.ysCustIncrQueryV2(reqDto);
        return ResultsBean.SUCCESS(rspDto);
    }

    /**
     * MCC查询-1294 实时查询MCC码
     *
     * @param req 查询参数 mccCd/mercType
     * @return 结果结构体
     */
    @RequestMapping(value = "/queryMcc", method = RequestMethod.POST)
    public YsMccQueryRspDto queryMcc(@RequestBody @Validated YsMccQueryReqDto reqDto) {
        YsMccQueryRspDto mccResult = ysCustIncrBizImpl.queryMcc(reqDto.getMccCd(), reqDto.getMercType());
        return mccResult;
    }

    /**
     * 地区信息查询-1296
     *
     * @param reqDto 查询参数
     * @return 结果结构体
     */
    @RequestMapping(value = "/queryArea", method = RequestMethod.POST)
    public YsAreaQueryRspDto queryArea(@RequestBody @Validated YsAreaQueryReqDto reqDto) {
        YsAreaQueryRspDto areaResult = ysCustIncrBizImpl.queryArea(reqDto);
        return areaResult;
    }

    /**
     * 拉取并保存所有地区信息到数据库
     */
    @GetMapping("/fetchAndSaveAllAreaInfo")
    public String fetchAndSaveAllAreaInfo() {
        int pageNumber = 1;
        int pageSize = 50;
        int maxPage = 1;
        do {
            YsAreaQueryReqDto reqDto = new YsAreaQueryReqDto();
            reqDto.setPageNumber(String.valueOf(pageNumber));
            reqDto.setPageSize(String.valueOf(pageSize));
            YsAreaQueryRspDto rsp = ysCustIncrBizImpl.queryArea(reqDto);
            if (rsp == null || rsp.getBusinessData() == null) break;
            YsAreaQueryRspDto.BusinessData businessData = rsp.getBusinessData();
            if (businessData.getAreaInfoList() != null && !businessData.getAreaInfoList().isEmpty()) {
                List<AreaInfo> areaList = new java.util.ArrayList<>();
                for (YsAreaQueryRspDto.AreaInfo info : businessData.getAreaInfoList()) {
                    AreaInfo entity = new AreaInfo();
                    BeanUtils.copyProperties(info, entity);
                    areaList.add(entity);
                }
                areaInfoService.saveBatch(areaList);
            }
            try {
                maxPage = Integer.parseInt(businessData.getMaxPage());
            } catch (Exception e) {
                break;
            }
            pageNumber++;
        } while (pageNumber <= maxPage);
        return "success";
    }


}
