package com.pay.channel.cloud.paysm.imports;

import cn.hutool.core.map.MapBuilder;
import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.exception.SDKException;
import com.pay.channel.beans.paysm.vo.lklsm.imports.LklSmNotify;
import com.pay.channel.biz.SendAlarmBiz;
import com.pay.channel.biz.paysm.exports.lklsm.LklSmExportsTransBizImpl;
import com.pay.channel.biz.paysm.imports.PaySmDataInputBiz;
import com.pay.channel.configuration.paysm.LklSmChannelOrApiConfig;
import com.pay.channel.entity.ChannelDataFlow;
import com.pay.channel.enums.ApiCodeLklSm;
import com.pay.channel.enums.ChannelPushType;
import com.pay.channel.enums.DataStatus;
import com.pay.channel.exception.ChannelPayException;
import com.pay.channel.service.ChannelDataFlowService;
import com.pay.channel.utils.JsonUtils;
import com.pay.channel.utils.LklSmRSAUtil;
import com.pay.frame.common.base.enums.Origin;
import com.pay.frame.common.base.util.StringUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR> @date 2025/7/15
 * @apiNote
 */
@Slf4j
@RestController
@RequestMapping("/lklSm/inputData")
public class LklSmDataInputProvider {

    @Autowired
    LklSmExportsTransBizImpl lklSmExportsTransBiz;

    @Resource
    private ChannelDataFlowService channelDataFlowService;

    @Resource
    private PaySmDataInputBiz paySmDataInputBiz;

    @Resource
    private SendAlarmBiz sendAlarmBiz;

    @Autowired
    private LklSmChannelOrApiConfig channelOrApiConfig;


    /**
     * 商户审核回调
     *
     * @param lklSmNotify
     * @return
     */
    @RequestMapping(value = "/custAuditNotify", method = RequestMethod.POST)
    public Map<String, Object> custAuditNotify(@RequestBody @Validated LklSmNotify lklSmNotify) {
        Map<String, Object> map = saasSubscribe(lklSmNotify, ApiCodeLklSm.NOTIFY_CUST_AUDIT);
        return map;
    }

    /**
     * 商户改件回调
     *
     * @param lklSmNotify
     * @return
     */
    @RequestMapping(value = "/custChangetNotify", method = RequestMethod.POST)
    public Map<String, Object> custChangetNotify(@RequestBody @Validated LklSmNotify lklSmNotify) {
        Map<String, Object> map = saasSubscribe(lklSmNotify, ApiCodeLklSm.NOTIFY_CUST_CHANGE);
        return map;
    }


    /**
     * 交易回调
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/transNotify", method = RequestMethod.POST)
    public Map<String, Object> lsCustAuditNotify(HttpServletRequest request) throws SDKException {
        try {
            String body = LKLSDK.notificationHandle(request);
            if (StringUtils.isBlank(body)) {
                throw new SDKException("交易通知 解密错误");
            }

            Map map = JsonUtils.json2Bean(body, Map.class);
            kfptSubscribe(body, map.get("out_trade_no").toString(), ApiCodeLklSm.NOTIFY_TRANS_FLOW, ChannelPushType.TRANS_LKLSM);
            return MapBuilder.<String, Object>create().put("code", "SUCCESS").put("message", "执行成功").build();
        } catch (Exception e) {
            log.error("交易订单通知 error", e);
            return MapBuilder.<String, Object>create().put("code", "ERROR").put("message", "接收失败").build();
        }
    }


    /**
     * 交易手续费回调
     *
     * @param lklSmNotify
     * @return
     */
    @RequestMapping(value = "/transFeeNotify", method = RequestMethod.POST)
    public Map<String, Object> transFeeNotify(@RequestBody @Validated LklSmNotify lklSmNotify) {
        Map<String, Object> map = saasSubscribe(lklSmNotify, ApiCodeLklSm.NOTIFY_TRANS_FEE_FLOW);
        return map;
    }

    /**
     * D0手续费回调
     *
     * @param lklSmNotify
     * @return
     */
    @RequestMapping(value = "/transD0FeeNotify", method = RequestMethod.POST)
    public Map<String, Object> transD0FeeNotify(@RequestBody @Validated LklSmNotify lklSmNotify) {
        Map<String, Object> map = saasSubscribe(lklSmNotify, ApiCodeLklSm.NOTIFY_TRANS_D0FEE_FLOW);
        return map;
    }


    /**
     * 开放平台
     *
     * @param
     * @param apiCode
     * @return
     */
    private Map<String, Object> kfptSubscribe(String data, String prNo, ApiCodeLklSm apiCode,
                                              ChannelPushType pushType) {
        try {
            String channelNo = channelOrApiConfig.getAppid();
            ChannelDataFlow dataFlow = channelDataFlowService.findByPrNo(prNo, channelNo, apiCode.name());
//            log.info("pyp 查询 dataFlow 是否存在：{} ", dataFlow);
            if (dataFlow != null) {
                if (DataStatus.SUCCESS.name().equals(dataFlow.getStatus())) {
                    log.info("LKLSM kfpt 该数据已经推送成功：{}", dataFlow);
                    return MapBuilder.<String, Object>create().put("code", "SUCCESS").put("message", "执行成功").build();
                } else {
                    dataFlow.setReqData(data);
                    dataFlow.setOrgData(data);
                    channelDataFlowService.pypUpdate(dataFlow);
                }
            } else {
                // 保存
                dataFlow = channelDataFlowService.decryptInsert(prNo, apiCode.name(), data, channelNo, Origin.LKLSM.name(), data, pushType);
            }

            paySmDataInputBiz.lklSmProcessData(dataFlow);
            return MapBuilder.<String, Object>create().put("code", "SUCCESS").put("message", "执行成功").build();
        } catch (Exception e) {
            sendAlarmBiz.sendDingDing(e.getMessage());
            log.error("推送通知异常 e：{},{}", pushType, data, e);
            return MapBuilder.<String, Object>create().put("code", "ERROR").put("message", "接收失败").build();
        }
    }


    /**
     * @param
     * @param apiCode
     * @return
     */
    @SneakyThrows
    private Map<String, Object> saasSubscribe(LklSmNotify lklSmNotify, ApiCodeLklSm apiCode) {
        ChannelDataFlow dataFlow = channelDataFlowService.findByPrNo(lklSmNotify.getId(),
                channelOrApiConfig.getClientId(),
                apiCode.getCode());
        log.info("LKLSM saas 查询 dataFlow 是否存在：{} ", dataFlow);
        if (dataFlow != null) {

            if (DataStatus.SUCCESS.name().equals(dataFlow.getStatus())) {
                log.info("LKLSM saas 该数据已经推送成功：{}", dataFlow);
                return MapBuilder.<String, Object>create().put("code", "SUCCESS").put("message", "执行成功").build();
            } else {

                dataFlow.setReqData(JsonUtils.bean2Json(lklSmNotify));
                channelDataFlowService.updateReqData(dataFlow);
            }

        } else {
            // 保存
            dataFlow = channelDataFlowService.insertReqData(lklSmNotify.getId(), apiCode.getCode(),
                    JsonUtils.bean2Json(lklSmNotify), channelOrApiConfig.getClientId(), Origin.LKLSM.name());
            log.info("LKLSM saas 初始化保存完成：{}", lklSmNotify.getId());
        }

        log.info("LKLSM saas processData start lklSmNotify:{}", JsonUtils.bean2Json(dataFlow));
        String decrypt = LklSmRSAUtil.decryptSaasCallBack(channelOrApiConfig.getSaasChannelPublicKey(), lklSmNotify.getData());
        dataFlow.setOrgData(decrypt);
        paySmDataInputBiz.lklSmProcessData(dataFlow);
        log.info("LKLSM saas processData end ");

        return MapBuilder.<String, Object>create().put("code", "SUCCESS").put("message", "执行成功").build();
    }


    /**
     * @param ex
     * @return
     * @Description 定义异常处理
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Object handlerException(Exception ex) {
        log.error("系统运行异常", ex);
        sendAlarmBiz.sendDingDing(ex.getMessage());
        return new ResponseEntity<>("Internal Server Error", HttpStatus.INTERNAL_SERVER_ERROR);
    }


}
