package com.pay.channel.beans.pay.dto.cust;

import com.pay.channel.beans.pay.dto.PayBaseRspDto;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> @date 2024/4/13
 * @apiNote
 */
@Data
@Accessors(chain=true)
@ToString(callSuper = true)
public class CustChangeFeeRspDto extends PayBaseRspDto {

    /**
     *
     */
    private String channelFlowNo;

    /**
     */
    private String channelStatus;

    /**
     *
     */
    private String message;

    /**
     *
     */
    private String channelMessage;


    //  ys

    /** 签约id */
    private String signId;
    /** 权限id */
    private String authId;
    /** 签约地址 */
    private String signUrl;
    //  ys end

}
