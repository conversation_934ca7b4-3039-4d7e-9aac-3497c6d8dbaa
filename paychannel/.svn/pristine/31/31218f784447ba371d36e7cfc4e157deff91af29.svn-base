package com.pay.channel.utils.converters.ys;

import com.pay.channel.exception.ChannelPayException;
import com.pay.frame.common.base.enums.BillStatus;

/**
 * <AUTHOR> @date 2025/7/31
 * @apiNote
 */
public class YsCustSignStatusConvUtil {


    /**
     * 状态,00-成功、01-初始化、02-签约中(电子合同才会出现此状态)、
     * 03-待审核(纸质合同或发起方不支持自动审核会出现此状态)、04-审核拒绝
     * @param code
     * @return
     */
    public static String code2SignStatus(String code) {
        switch (code) {
            case "00":
                return BillStatus.SUCCESS.name();
            case "01":
                return BillStatus.INIT.name();
            case "02":
            case "03":
                return BillStatus.AUDITING.name();
            case "04":
                return BillStatus.FAIL.name();
            default:
                throw new ChannelPayException("不支持的商户签约状态异常 ： " + code);
        }
    }

}
