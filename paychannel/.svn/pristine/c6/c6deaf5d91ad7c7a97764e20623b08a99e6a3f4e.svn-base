package com.pay.channel.beans.pay.dto.custIncr;

import lombok.Data;

/**
 * <AUTHOR> @date 2025/7/31
 * @apiNote
 */
@Data
public class YsCustInSignQueryV2RspDto {

    /**
     * 状态,00-成功、01-初始化、02-签约中(电子合同才会出现此状态)、
     * 03-待审核(纸质合同或发起方不支持自动审核会出现此状态)、04-审核拒绝。
     */
    private String status;

    /**
     * 签约流水号,电子合同才有值
     */
    private String signId;

    /**
     * 权限流水号
     */
    private String authId;

    /**
     * 渠道商户号,成功时才有值
     */
    private String mercId;

    /**
     * 备注,审核拒绝才有值
     */
    private String note;

    /**
     * 业务方权限id
     */
    private String thirdAuthId;

}
