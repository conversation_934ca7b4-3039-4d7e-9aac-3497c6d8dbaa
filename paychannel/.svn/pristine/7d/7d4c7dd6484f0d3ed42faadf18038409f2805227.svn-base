package com.pay.channel.cloud.exports.ys;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pay.channel.BaseTest;
import com.pay.channel.beans.pay.dto.FeeBean;
import com.pay.channel.beans.pay.dto.custIncr.*;
import com.pay.channel.beans.pay.vo.ys.exports.cust.YsCustInSignQueryV2Req;
import com.pay.channel.beans.pay.vo.ys.exports.cust.YsCustInfoSignReq;
import com.pay.channel.beans.pay.vo.ys.exports.cust.YsCustInfoSignRes;
import com.pay.channel.biz.pay.exports.ys.YsPayExportsBiz;
import com.pay.channel.cloud.pay.exports.PayCustIncrProvider;
import com.pay.channel.configuration.ApiConfig;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.enums.CardType;
import com.pay.frame.common.base.enums.Origin;
import com.pay.frame.common.base.enums.TransWay;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @date 2024/11/20
 * @apiNote
 */
public class YsCustIncrProviderTest extends BaseTest {

    @Resource
    PayCustIncrProvider payCustIncrProvider;

    @Resource
    private YsPayExportsBiz ysPayExportsBiz;

    @Resource
    private ApiConfig apiConfig;

    /**
     * {"mercId":"826134057128008","notSuccess":false,"ysCode":"0000","success":true,"ysMsg":"业务成功","state":"00","customerNo":"67291038940"}
     * 67291038940    保定
     *
     * {"mercId":"826602057128037","notSuccess":false,"ysCode":"0000","success":true,"ysMsg":"业务成功","state":"00","customerNo":"67291038952"}
     * 67291038952    东莞
     *
     * {"mercId":"826602057128038","notSuccess":false,"ysCode":"0000","success":true,"ysMsg":"业务成功","state":"00","customerNo":"67291038953"}
     *
     * {"mercId":"826602057128039","notSuccess":false,"ysCode":"0000","success":true,"ysMsg":"业务成功","state":"00","customerNo":"67291038954"}
     *
     * {"mercId":"826602057128040","notSuccess":false,"ysCode":"0000","success":true,"ysMsg":"业务成功","state":"00","customerNo":"67291038955"}
     *
     * {"mercId":"826602057128041","notSuccess":false,"ysCode":"0000","success":true,"ysMsg":"业务成功","state":"00","customerNo":"67291038956"}
     *
     * {"mercId":"826602057128042","notSuccess":false,"ysCode":"0000","success":true,"ysMsg":"业务成功","state":"00","customerNo":"67291038957"}
     *
     *
     * YsCustIncrRsp(super=com.pay.channel.beans.pay.vo.ys.exports.cust.YsCustIncrRsp@11c08cab, customerNo=67291038958, mercId=826602050948095, state=00, ysCode=0000, ysMsg=业务成功)
     */
    @Test
    public void ysCustInfoIncr(){
        YsCustIncrReqDto reqDto = new YsCustIncrReqDto();
        reqDto.setThirdCustNo("");
        reqDto.setFullName("晋城市默默村驻马店佳买车1");
        reqDto.setShortName("晋城市驻马店佳创买车1");
        reqDto.setLegalPerson("王五");
        reqDto.setIdentityNo("37130219931211183X");
        reqDto.setIdentityExpData("20551212");
        reqDto.setIdFrontFileId("Gq3FZxacGL/8GJ75RYyeti2d+5niD5YVDxryk24fTee5M+WvJtXfqfvMfvn6Iob8NQmNN8bOWw7GB+gRsPIsDw==");
        reqDto.setIdBackFileId("Gq3FZxacGL/8GJ75RYyeti2d+5niD5YVDxryk24fTee5M+WvJtXfqfvMfvn6Iob8NQmNN8bOWw7GB+gRsPIsDw==");
        reqDto.setIdCardHdFileId("Gq3FZxacGL/8GJ75RYyeti2d+5niD5YVDxryk24fTee5M+WvJtXfqfvMfvn6Iob8NQmNN8bOWw7GB+gRsPIsDw==");
        reqDto.setLinkman("王五");
        reqDto.setLinkPhone("***********");
        reqDto.setLinkEmail("<EMAIL>");
        reqDto.setLinkAddress("北京详细地址");
        reqDto.setProvinceCode("1600");
        reqDto.setCityCode("1680");
        reqDto.setDistrictCode("1684");
        reqDto.setAddress("山西省-晋中市-默默村");
        reqDto.setMccCode("5193");
        reqDto.setCustTypeCode("015");
        reqDto.setCustType("IND");
//        reqDto.setBusinessLicenseNo();
//        reqDto.setLicenseAddress();
//        reqDto.setLicenseExpData();
//        reqDto.setLicenseFileId();

        reqDto.setOrigin(Origin.YS.name());
        ResultsBean<YsCustIncrRspDto> resultsBean = payCustIncrProvider.ysCustInfoIncr(reqDto);
        System.out.println(resultsBean);
    }

    // {"code":"00000","subCode":"0000",
    // "businessData":{"sysFlowId":"APPL202507222059150043443","outCustomerNo":"826602057128042","customerNo":"***********"}
    @Test
    public void ysCustInfoIncrV2() {
        YsCustIncrReqV2Dto req = new YsCustIncrReqV2Dto();
        req.setSysFlowId("APPL202507222059150043443");
        YsCustIncrReqV2Dto.CustInfo custInfo = new YsCustIncrReqV2Dto.CustInfo();
        custInfo.setOutCustomerNo("826602057128042");
        custInfo.setMercName("晋城市默默村驻马店佳买车1");
        custInfo.setMercShortName("晋城市驻马店佳创买车1");
        custInfo.setMccCd("5713");
        custInfo.setMercType("2");
        custInfo.setContactMail("<EMAIL>");
        custInfo.setCusMgrNm("刘博文");
        req.setCustInfo(custInfo); // 基本信息

        YsCustIncrReqV2Dto.CrpInfo crpInfo = new YsCustIncrReqV2Dto.CrpInfo();
        crpInfo.setCrpCertNo("110105199102092510");
        crpInfo.setCrpCertType("00");
        crpInfo.setCertBgn("********");
        crpInfo.setCertExpire("********");
        crpInfo.setCrpNm("刘博文");
        crpInfo.setCrpPhone("***********");
        req.setCrpInfo(crpInfo);

        YsCustStlAccInfoDto stlAccInfo = new YsCustStlAccInfoDto();
        stlAccInfo.setStlAccNo("6212260200003695406");
        stlAccInfo.setBankSubCode("************");
        stlAccInfo.setStlAccType("11");
        req.setStlAccInfo(stlAccInfo);

        YsCustIncrReqV2Dto.BusInfo busInfo = new YsCustIncrReqV2Dto.BusInfo();
        busInfo.setBusNm("晋城市驻马店佳创买车1");
        busInfo.setBusProviceCode("1600");
        busInfo.setBusCityCode("1680");
        busInfo.setBusAreaCode("1684");
        busInfo.setBusAddr("山西省-晋中市-默默村");
        req.setBusInfo(busInfo);

        req.setOrigin(Origin.YS.name());
        ResultsBean<YsCustIncrRspV2Dto> resultsBean = payCustIncrProvider.ysCustInfoIncrV2(req);
        System.out.println(resultsBean);
    }


    @Test
    public void ysCustInfoFileV2() {

        YsCustInfoFileReqDto A002 = new YsCustInfoFileReqDto();
        A002.setFileName(System.currentTimeMillis()+".png");
        A002.setFssId("Rj8ukLM6vT5BzH9EhAypLIqAYNiwNkVZHI+BQLT/dH8mU82dk2bTlXZSudVGoXJtNQmNN8bOWw7GB+gRsPIsDw==");
        A002.setPicType("A002"); // A002—法人身份证人像面[必填]
        A002.setSysFlowId("APPL202507222059150043443");
        A002.setCustomerNo("***********");

        YsCustInfoFileReqDto A003 = new YsCustInfoFileReqDto();
        A003.setFileName("24120717251458172307.png");
        A003.setFssId("Rj8ukLM6vT5BzH9EhAypLKzTOTSvawXORCUIYR/20BSJUXJGwH4+9Q8YQhvRNI4yNQmNN8bOWw7GB+gRsPIsDw==");
        A003.setPicType("A003"); // A003—法人身份证国徽面[必填]
        A003.setSysFlowId("APPL202507222059150043443");
        A003.setCustomerNo("***********");

        YsCustInfoFileReqDto A004 = new YsCustInfoFileReqDto();
        A004.setFileName("24120717251804220076.png");
        A004.setFssId("Rj8ukLM6vT5BzH9EhAypLAYO+obQ0hHKeKvL2wb66aYS6lQUfT89HeGqXLWpyJ7GNQmNN8bOWw7GB+gRsPIsDw==");
        A004.setPicType("A004"); // A004—结算银行卡正面（有卡号一面）[必填]
        A004.setSysFlowId("APPL202507222059150043443");
        A004.setCustomerNo("***********");

        YsCustInfoFileReqDto A005 = new YsCustInfoFileReqDto();
        A005.setFileName("24120715334403277763.png");
        A005.setFssId("Rj8ukLM6vT5BzH9EhAypLAYO+obQ0hHKeKvL2wb66aYS6lQUfT89HeGqXLWpyJ7GNQmNN8bOWw7GB+gRsPIsDw==");
        A005.setPicType("A005"); // A005—结算银行卡反面[必填]
        A005.setSysFlowId("APPL202507222059150043443");
        A005.setCustomerNo("***********");

        YsCustInfoFileReqDto A006 = new YsCustInfoFileReqDto();
        A006.setFileName("24120717252217452002.png");
        A006.setFssId("VaQXQxcFLiaQMOG8AqJz0eiR6Sq1gmgL2Wf5Ek+3zjKWhtiLO9fa17yUjGkX0UKgNQmNN8bOWw7GB+gRsPIsDw==");
        A006.setPicType("A006"); // A006—商户门头照[必填]
        A006.setSysFlowId("APPL202507222059150043443");
        A006.setCustomerNo("***********");

        YsCustInfoFileReqDto A007 = new YsCustInfoFileReqDto();
        A007.setFileName("24120717252215346210.png");
        A007.setFssId("VaQXQxcFLiaQMOG8AqJz0bO0GYs/+730IcbBe/0SI6s5+HfHwzRRsJCkCowZD3tyNQmNN8bOWw7GB+gRsPIsDw==");
        A007.setPicType("A007"); // A007—商户经营场所照[必填]
        A007.setSysFlowId("APPL202507222059150043443");
        A007.setCustomerNo("***********");

        YsCustInfoFileReqDto A008 = new YsCustInfoFileReqDto();
        A008.setFileName("24120717252213581377.png");
        A008.setFssId("VaQXQxcFLiaQMOG8AqJz0S+ZYLQvzuSkncgIwMYmdkzb5BvLBp17S/fe3APyM2qZNQmNN8bOWw7GB+gRsPIsDw==");
        A008.setPicType("A008"); // A008—商户收银台照[必填]
        A008.setSysFlowId("APPL202507222059150043443");
        A008.setCustomerNo("***********");


        List<YsCustInfoFileReqDto> list = new ArrayList<>();
//        list.add(A002);
//        list.add(A003);
        list.add(A004);
        list.add(A005);
//        list.add(A006);
//        list.add(A007);
//        list.add(A008);
        for (YsCustInfoFileReqDto reqDto : list){
            ResultsBean<String> resultsBean = payCustIncrProvider.ysCustFileIncr(reqDto);
            System.out.println(resultsBean);
        }

    }

    /**
     * 资料确认
     * "sysFlowId":"APPL202507222059150043443","custId":"2025072406062283","status":"00"
     */
    @Test
    public void ysCustInAudit() {
        YsCustInfoAuditReqV2Dto reqV2Dto = new YsCustInfoAuditReqV2Dto();
        reqV2Dto.setCustomerNo("***********");
        reqV2Dto.setAuditFlag("Y");
        reqV2Dto.setSysFlowId("APPL202507222059150043443");
        ResultsBean<YsCustInfoAuditResV2Dto> resultsBean = payCustIncrProvider.ysCustInAudit(reqV2Dto);
        System.out.println(resultsBean);
    }

    /**
     *
     */
    @Test
    public void ysCustIncrQueryV2(){
        YsCustInfoQueryReqV2Dto reqDto = new YsCustInfoQueryReqV2Dto();
        reqDto.setSysFlowId("APPL202507302056400044104");
        ResultsBean<YsCustInfoQueryResV2Dto> resultsBean = payCustIncrProvider.ysCustIncrQueryV2(reqDto);
        System.out.println(resultsBean);
    }

    /**
     * code=00000, subCode=0000, subMsg=成功, signId=MAN202507240023769, authId=AUTH20250724205625033488, signUrl=https://ctest.ysepay-test.com/fyfxsbwjw1)
     */
    @Test
    public void ysCustInSignV2(){

        YsCustInfoSignAndMdyFeeReqV2Dto.CodeScanT1Fee codeScanT1Fee = new YsCustInfoSignAndMdyFeeReqV2Dto.CodeScanT1Fee();
        YsCustInfoSignAndMdyFeeReqV2Dto.SwCardT1Fee swCardT1Fee = new YsCustInfoSignAndMdyFeeReqV2Dto.SwCardT1Fee();
        YsCustInfoSignAndMdyFeeReqV2Dto.WxFee wxFee = new YsCustInfoSignAndMdyFeeReqV2Dto.WxFee();
        wxFee.setRateType("0");
        wxFee.setRateFee("0.6");
        wxFee.setRateBottom("1000");
        codeScanT1Fee.setWxPayFee(wxFee);

        YsCustInfoSignAndMdyFeeReqV2Dto.AliPayFee aliPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.AliPayFee();
        aliPayFee.setRateType("0");
        aliPayFee.setRateFee("0.6");
        codeScanT1Fee.setAliPayFee(aliPayFee);

        YsCustInfoSignAndMdyFeeReqV2Dto.AliPayDebitFee aliPayDebitFee = new YsCustInfoSignAndMdyFeeReqV2Dto.AliPayDebitFee();
        aliPayDebitFee.setRateType("0");
        aliPayDebitFee.setRateFee("0.6");
        aliPayDebitFee.setRateBottom("1000");
        codeScanT1Fee.setAliPayDebitFee(aliPayDebitFee);
        YsCustInfoSignAndMdyFeeReqV2Dto.AliPayCreditFee aliPayCreditFee = new YsCustInfoSignAndMdyFeeReqV2Dto.AliPayCreditFee();
        aliPayCreditFee.setRateType("0");
        aliPayCreditFee.setRateFee("0.6");
        aliPayCreditFee.setRateBottom("1000");
        codeScanT1Fee.setAliPayCreditFee(aliPayCreditFee);

        YsCustInfoSignAndMdyFeeReqV2Dto.Bank1DebitPayFee bank1debitPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.Bank1DebitPayFee();
        bank1debitPayFee.setRateType("0");
        bank1debitPayFee.setRateFee("0.6");
        bank1debitPayFee.setRateBottom("1000");
        codeScanT1Fee.setBank1debitPayFee(bank1debitPayFee);

        YsCustInfoSignAndMdyFeeReqV2Dto.Bank2DebitPayFee bank2debitPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.Bank2DebitPayFee();
        bank2debitPayFee.setRateType("0");
        bank2debitPayFee.setRateFee("0.6");
        bank2debitPayFee.setRateBottom("10");
        codeScanT1Fee.setBank2debitPayFee(bank2debitPayFee);

        YsCustInfoSignAndMdyFeeReqV2Dto.Bank1CreditPayFee bank1CreditPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.Bank1CreditPayFee();
        bank1CreditPayFee.setRateType("0");
        bank1CreditPayFee.setRateFee("0.6");
        bank1CreditPayFee.setRateBottom("1000");
        codeScanT1Fee.setBank1creditPayFee(bank1CreditPayFee);

        YsCustInfoSignAndMdyFeeReqV2Dto.Bank2CreditPayFee bank2CreditPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.Bank2CreditPayFee();
        bank2CreditPayFee.setRateType("0");
        bank2CreditPayFee.setRateFee("0.6");
        bank2CreditPayFee.setRateBottom("10");
        codeScanT1Fee.setBank2creditPayFee(bank2CreditPayFee);

        YsCustInfoSignAndMdyFeeReqV2Dto.DebitPayFee debitPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.DebitPayFee();
        debitPayFee.setRateType("0");
        debitPayFee.setRateFee("0.6");
        debitPayFee.setRateBottom("1000");
        debitPayFee.setRateTop("100000");
        swCardT1Fee.setDebitPayFee(debitPayFee);

        YsCustInfoSignAndMdyFeeReqV2Dto.CreditPayFee creditPayFee = new YsCustInfoSignAndMdyFeeReqV2Dto.CreditPayFee();
        creditPayFee.setRateType("0");
        creditPayFee.setRateFee("0.6");
        creditPayFee.setRateBottom("1000");
        swCardT1Fee.setCreditPayFee(creditPayFee);

        YsCustInfoSignAndMdyFeeReqV2Dto reqV2Dto = new YsCustInfoSignAndMdyFeeReqV2Dto();
        reqV2Dto.setCodeScanT1Fee(codeScanT1Fee);
        reqV2Dto.setSwCardT1Fee(swCardT1Fee);
        reqV2Dto.setCustomerNo("***********");
        reqV2Dto.setCustId("****************");


        YsCustInfoSignReq req = YsCustInfoSignReq.convReq(reqV2Dto);


         String  interType = apiConfig.getYsCustInfoSign();

        YsCustInfoSignRes rsp = ysPayExportsBiz.biz(interType, req, "银盛商户费率修改",
                new TypeReference<YsCustInfoSignRes>() {
                });
        System.out.println(rsp);
//        ResultsBean<CustChangeFeeRspDto> resultsBean = payCustIncrProvider.ysCustInSignV2(reqV2Dto);
//        System.out.println(resultsBean);

    }

    @Test
    public void ysCustInSignQueryV2(){
        YsCustInSignQueryV2Req reqV2Dto = new YsCustInSignQueryV2Req();
        reqV2Dto.setCustomerNo("***********");
        reqV2Dto.setAuthId("AUTH20250731103346033982");
        ResultsBean<YsCustInSignQueryV2RspDto> resultsBean = payCustIncrProvider.ysCustInSignQueryV2(reqV2Dto);
        System.out.println(resultsBean);
    }

    /**
     *
     */
    @Test
    public void ysCustSettleAccIncr(){
        YsCustSettleAccIncrReqDto reqDto = new YsCustSettleAccIncrReqDto();
        reqDto.setThirdCustNo("***********");
        reqDto.setBankAccountNo("6222804048714993617");
        reqDto.setBankAccountName("王五");
        reqDto.setHeadBankCode("1021000");
        reqDto.setAlliedBankCode("************");
        reqDto.setAlliedBankName("中国工商银行股份有限公司北京新华支行");
        reqDto.setProvinceCode("3600");
        reqDto.setCityCode("3810");
        reqDto.setPrePhone("***********");
        reqDto.setCardFrontFileId("Gq3FZxacGL/8GJ75RYyeti2d+5niD5YVDxryk24fTee5M+WvJtXfqfvMfvn6Iob8NQmNN8bOWw7GB+gRsPIsDw==");
        reqDto.setAccountType("INDIVIDUAL");
        reqDto.setIsLegal("Y");
//        reqDto.setRptAccountType("");
//        reqDto.setRptAccountNo("");
//        reqDto.setRptAccountName("");
//        reqDto.setRptAlliedBankCode("");
//        reqDto.setRptCardFrontFileId("Gq3FZxacGL/8GJ75RYyeti2d+5niD5YVDxryk24fTee5M+WvJtXfqfvMfvn6Iob8NQmNN8bOWw7GB+gRsPIsDw==");

        reqDto.setOrigin(Origin.YS.name());
        ResultsBean<YsCustSettleAccIncrRspDto> resultsBean = payCustIncrProvider.ysCustSettleAccIncr(reqDto);
        System.out.println(resultsBean);
    }


    /**
     *
     */
    @Test
    public void ysCustFeeIncr(){
        YsCustFeeIncrReqDto reqDto = new YsCustFeeIncrReqDto();
        reqDto.setThirdCustNo("***********");

        List<FeeBean> listFee = new ArrayList<>();
        initFeeBean(CardType.CREDIT_CARD, "0.006", TransWay.YHK, listFee);
        initFeeBean(CardType.DEBIT_CARD, "0.006", TransWay.YHK, listFee);
        initFeeBean(CardType.DEFAULT, "0.004", TransWay.WX, listFee);
//        initFeeBean(CardType.DEFAULT, "0.004", TransWay.ZFB, listFee);
//        initFeeBean(CardType.DEFAULT, "0.004", TransWay.YL, listFee);
        reqDto.setListFee(listFee);

        reqDto.setOrigin(Origin.YS.name());
        ResultsBean<YsCustFeeIncrRspDto> resultsBean = payCustIncrProvider.ysCustFeeIncr(reqDto);
        System.out.println(resultsBean);
    }



    public void initFeeBean(CardType cardType, String rate, TransWay transWay, List<FeeBean> listFee){
        FeeBean feeBean = new FeeBean();
        feeBean.setCardType(cardType);
        feeBean.setRate(new BigDecimal(rate));
        feeBean.setTransWay(transWay);
        listFee.add(feeBean);
    }


    /**
     *
     */
    @Test
    public void lsIncrQuery(){
        CustIncrQueryReqDto reqDto = new CustIncrQueryReqDto();
        reqDto.setThirdCustNo("**********");
        ResultsBean<LsCustIncrQueryRspDto> resultsBean = payCustIncrProvider.lsIncrQuery(reqDto);
        System.out.println(resultsBean);
    }



}
