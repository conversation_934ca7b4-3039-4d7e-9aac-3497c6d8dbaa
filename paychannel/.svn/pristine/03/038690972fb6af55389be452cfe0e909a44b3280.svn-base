package com.pay.channel.beans.pay.vo.ys.exports.cust;

import com.pay.channel.beans.pay.dto.cust.CustChangeFeeRspDto;
import com.pay.channel.beans.pay.dto.custIncr.YsCustInfoSignResV2Dto;
import com.pay.channel.enums.ChannelPosCustStatus;
import com.pay.channel.exception.ChannelPayException;
import com.pay.frame.common.base.util.StringUtils;
import lombok.Data;

/**
 * @BelongsProject: 20250714-LKL
 * @BelongsPackage: com.pay.channel.beans.pay.vo.ys.exports.cust
 * @Author: xingwei.wang
 * @CreateTime: 2025-07-24  20:17
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class YsCustInfoSignRes {

    private String code;

    private String msg;

    /**
     * 业务响应码
     */
    private String subCode;

    /**
     * 业务响应描述
     */
    private String subMsg;

    private YsCustInfoSignResBusinessDataRes businessData;


    @Data
    public static class YsCustInfoSignResBusinessDataRes {
        /**
         * 签约id
         */
        private String signId;

        /**
         * 权限id
         */
        private String authId;

        /**
         * 签约地址
         */
        private String signUrl;
    }

    public YsCustInfoSignResV2Dto convRspDto() {
        if (!"0000".equals(subCode)) {
            throw new ChannelPayException(subCode, subMsg);
        }
        YsCustInfoSignResV2Dto rspDto = new YsCustInfoSignResV2Dto();
        rspDto.setCode(code);
        rspDto.setSubCode(subCode);
        rspDto.setSubMsg(subMsg);
        if(businessData != null){
            rspDto.setSignId(businessData.getSignId());
            rspDto.setAuthId(businessData.getAuthId());
            rspDto.setSignUrl(businessData.getSignUrl());
        }
        
        return rspDto;
    }

    public CustChangeFeeRspDto convFeeRspDto() {
        if(!"00000".equals(code)){
            throw new ChannelPayException(code, msg);
        }
        if(!"0000".equals(subCode)){
            String message = StringUtils.isNotBlank(subMsg) ? subMsg : msg;
            throw new ChannelPayException(subCode, message);
        }

        CustChangeFeeRspDto rspDto = new CustChangeFeeRspDto();
        rspDto.setChannelStatus("0000".equals(subCode)? ChannelPosCustStatus.SUCCESS.name():ChannelPosCustStatus.FAIL.name());
        rspDto.setMessage(subMsg);
        rspDto.setChannelMessage(subCode);
        rspDto.setSignId(this.getBusinessData().getSignId());
        rspDto.setSignUrl(this.getBusinessData().getSignUrl());
        rspDto.setAuthId(this.getBusinessData().getAuthId());
        return rspDto;
    }
}
