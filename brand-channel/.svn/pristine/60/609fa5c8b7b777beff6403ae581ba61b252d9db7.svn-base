package com.pay.brand.channel.core.infrastructure.enums;

import com.pay.frame.common.base.enums.YesNo;

/**
 * API code 枚举类
 *
 * <AUTHOR>
 */

public enum ApiCode {

    SIGN_IN("0000","签到", YesNo.Y, YesNo.N, YesNo.Y),
    SIGN_OUT("0001","签退", YesNo.Y, YesNo.N, YesNo.Y),
    CUSTOMER_INCR_QUERY("1001","商户入网查询", YesNo.N, YesNo.N, YesNo.Y),
    CUSTOMER_INCR("1002","商户入网", YesNo.N, YesNo.N, YesNo.Y),
    CUSTOMER_FEE_CHANGE("1003","商户费率变更", YesNo.N, YesNo.N, YesNo.Y),
    CUSTOMER_CUST_CARD_CHANGE("1004","商户结算卡变更", YesNo.N, YesNo.N, YesNo.Y),
    CUST_UNION_QUERY("1005","银联商户查询", YesNo.N, YesNo.N, YesNo.Y),
    CUST_INCR_UPLOAD_FILE("1006","商户入网文件上传", YesNo.N, YesNo.N, YesNo.Y),
    CUST_SL_UPLOAD_FILE("1007","商户双录文件上传", YesNo.N, YesNo.N, YesNo.Y),
    CUST_BATCH_SIM_CARD_FEE("1009","批量SIM卡费设置", YesNo.N, YesNo.N, YesNo.Y),
    CUSTOMER_QR_REPORT("1010","商户二维码报备", YesNo.N, YesNo.N, YesNo.Y),
    CUSTOMER_QR_FIND("1011","商户二维码报备查询", YesNo.N, YesNo.N, YesNo.Y),
    CUST_ONE_TO_N_REUSE("1012","1拖N绑定复用商户", YesNo.N, YesNo.N, YesNo.Y),
    CUST_ONE_TO_N_QUERY("1013","1拖N渠道商户查询", YesNo.N, YesNo.N, YesNo.Y),
    CUST_RPT_RETRY("1014","重新报备", YesNo.N, YesNo.N, YesNo.Y),
    CUST_INFO_CHANGE("1015","商户信息变更", YesNo.N, YesNo.N, YesNo.Y),

    CUSTOMER_INFO_NOTIFY("1102","商户基本信息通知", YesNo.Y, YesNo.Y, YesNo.Y),
    CUSTOMER_FEE_CHANGE_NOTIFY("1103","商户手续费变更通知", YesNo.Y, YesNo.Y, YesNo.Y),

    POS_CHANGE_ORIGIN("2001","终端变更品牌", YesNo.N, YesNo.N, YesNo.Y),
    POS_UNBIND("2002","终端解绑", YesNo.N, YesNo.N, YesNo.Y),
    POS_BIND("2003","终端绑定", YesNo.N, YesNo.N, YesNo.Y),
    POS_CHANGE_POLICY("2004", "终端活动变更", YesNo.N, YesNo.N, YesNo.Y),
    POS_BIND_CHECK_SL("2005","终端绑定-双录校验", YesNo.N, YesNo.N, YesNo.Y),
    BATCH_POS_CHANGE_POLICY("2008","批量终端活动变更", YesNo.N, YesNo.N, YesNo.Y),
    POS_STATUS_CHANGE_NOTIFY("2101","终端状态变更通知", YesNo.Y, YesNo.Y, YesNo.Y),
    POS_CHECK_BIND("1205","终端绑定验证查询", YesNo.Y, YesNo.Y, YesNo.Y),

    CUST_RETURN_FEE("1206","商户返服务费查询", YesNo.Y, YesNo.Y, YesNo.Y),
    
    TRANS_FLOW_NOTIFY("3101","交易流水通知", YesNo.Y, YesNo.Y, YesNo.Y),
    
    ACCOUNT_BALANCE_INQUIRY("4001","账户余额查询", YesNo.N, YesNo.N, YesNo.Y),
    
    PAYMENT_REQUEST_INITIATED("4002","付款-请求发起", YesNo.N, YesNo.N, YesNo.Y),
    PAYMENT_RESULT_QUERY("4003","付款-结果查询", YesNo.N, YesNo.N, YesNo.Y),
    PAYMENT_NA_REQUEST_INITIATED("4004","付款-免审请求发起", YesNo.N, YesNo.N, YesNo.Y),
    PAYMENT_REGISTER_WITHDRAW("4005", "付款-(报备后)请求发起", YesNo.N, YesNo.N, YesNo.Y),

    ORGANIZATION_QUERY("5001","区域查询", YesNo.N, YesNo.N, YesNo.Y),
    MCC_QUERY("5002","Mcc查询", YesNo.N, YesNo.N, YesNo.Y),
    HEAD_BANK_QUERY("5003","总行查询", YesNo.N, YesNo.N, YesNo.Y),
//    SUB_BANK_QUERY("5004","支行查询", YesNo.N, YesNo.N, YesNo.Y),
    SL_CHECK_BACK_URL("5005","双录校验并发起", YesNo.N, YesNo.N, YesNo.Y),
    SL_CHECK_RESULT("5006","双录结果查询", YesNo.N, YesNo.N, YesNo.Y),
    ALI_OSS_TEMP_TOKEN("5007","文件上传获取临时token", YesNo.N, YesNo.N, YesNo.Y),
    MARKETING_RECONCILIATION_FILE_DOWNLOAD("9002","营销对账文件下载", YesNo.N, YesNo.N, YesNo.Y),
    IND_CUST_Query("9003","查询个体商户信息", YesNo.N, YesNo.N, YesNo.Y),
    
    
    //pyp
    PYP_CUSTOMER_INCR_QUERY("PYP1001","PYP_商户入网查询", YesNo.N, YesNo.N, YesNo.Y),
    PYP_CUSTOMER_INCR("PYP1002","PYP_商户入网", YesNo.N, YesNo.N, YesNo.Y),
    PYP_CUSTOMER_INCR_AX_SIGN("PYP1003","PYP_商户入网签约安心", YesNo.N, YesNo.N, YesNo.Y),
    PYP_CUSTOMER_CUST_CARD_CHANGE("PYP1004","PYP_商户结算卡变更", YesNo.N, YesNo.N, YesNo.Y),
    PYP_CUSTOMER_CUST_CARD_CHANGE_FIND("PYP1005","PYP_商户结算卡变更查询", YesNo.N, YesNo.N, YesNo.Y),

    PYP_CUST_INCR_UPLOAD_FILE("PYP1006","PYP_商户入网文件上传", YesNo.N, YesNo.N, YesNo.Y),
    PYP_QR_RPT_QUERY("PYP1011","PYP_报备信息查询", YesNo.N, YesNo.N, YesNo.Y),
    
    PYP_YS_SIGN_QUERY("PYP1101","PYP_ys签约查询", YesNo.N, YesNo.N, YesNo.Y),
    PYP_YS_SIGN_APPLY("PYP1102","PYP_ys签约申请", YesNo.N, YesNo.N, YesNo.Y),
    
    PYP_POS_CHANGE_ORIGIN("PYP2001","PYP_终端变更品牌", YesNo.N, YesNo.N, YesNo.Y),
    PYP_POS_UNBIND("PYP2002","PYP_终端解绑", YesNo.N, YesNo.N, YesNo.Y),
    PYP_POS_BIND("PYP2003","PYP_终端绑定", YesNo.N, YesNo.N, YesNo.Y),
    
    
    PYP_MCC_LARGE_QUERY("PYP5000","PYP_行业大类查询", YesNo.N, YesNo.N, YesNo.Y),
    PYP_ORGANIZATION_QUERY("PYP5001","PYP_区域查询", YesNo.N, YesNo.N, YesNo.Y),
    PYP_MCC_QUERY("PYP5002","PYP_行业查询", YesNo.N, YesNo.N, YesNo.Y),
    PYP_HEAD_BANK_QUERY("PYP5003","PYP_总行查询", YesNo.N, YesNo.N, YesNo.Y),

    PYP_ALI_OSS_TEMP_TOKEN("PYP5007","PYP_文件上传获取临时token", YesNo.N, YesNo.N, YesNo.Y),

    //---NOTIFY
    PYP_TRANS_FLOW_NOTIFY("PYP3101","PYP_交易流水通知", YesNo.Y, YesNo.Y, YesNo.Y),

    LKLSM_TRANS_FLOW_NOTIFY("LKLSM3101","LKLSM_交易流水通知", YesNo.Y, YesNo.Y, YesNo.Y),



    /** ==================================== PAYSM ==========================================================  */
    PAYSM_CUSTOMER_INCR_QUERY("PAYSM1001","PAYSM_商户入网查询", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_CUSTOMER_INCR("PAYSM1002","PAYSM_商户入网", YesNo.N, YesNo.N, YesNo.Y),

    PAYSM_CUST_CARD_CHANGE("PAYSM1004","PAYSM_商户结算卡变更", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_CUST_CARD_CHANGE_FIND("PAYSM1005","PAYSM_商户结算卡变更查询", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_CUST_FEE_CHANGE("PAYSM1006","PAYSM_商户费率变更", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_CUST_FEE_CHANGE_FIND("PAYSM1007","PAYSM_商户费率变更查询", YesNo.N, YesNo.N, YesNo.Y),


    PAYSM_QR_RPT_QUERY("PAYSM1011","PAYSM_报备信息查询", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_CUST_INCR_UPLOAD_FILE("PAYSM1012","PAYSM_商户入网文件上传", YesNo.N, YesNo.N, YesNo.Y),


    PAYSM_INCR_SIGN_QUERY("PAYSM1101","PAYSM_签约查询", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_INCR_SIGN_APPLY("PAYSM1102","PAYSM_签约申请", YesNo.N, YesNo.N, YesNo.Y),



    PAYSM_MCC_LARGE_QUERY("PAYSM5000","PAYSM_行业大类查询", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_MCC_QUERY("PAYSM5001","PAYSM_行业查询", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_ORGANIZATION_QUERY("PAYSM5002","PAYSM_区域查询", YesNo.N, YesNo.N, YesNo.Y),
    PAYSM_HEAD_BANK_QUERY("PAYSM5004","PAYSM_总行查询", YesNo.N, YesNo.N, YesNo.Y),

    //---NOTIFY
    PAYSM_TRANS_FLOW_NOTIFY("PAYSM3101","PAYSM_交易流水通知", YesNo.Y, YesNo.Y, YesNo.Y),
    /** ==================================== PAYSM ==========================================================  */

    
    ;
    private String code;

    private String description;

    private YesNo isDefault;

    private YesNo isPush;

    private YesNo isShow;


    ApiCode(String code, String description, YesNo isDefault, YesNo isPush, YesNo isShow) {
        this.code = code;
        this.description = description;
        this.isDefault = isDefault;
        this.isPush = isPush;
        this.isShow = isShow;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public YesNo getIsDefault() {
        return isDefault;
    }

    public YesNo getIsPush() {
        return isPush;
    }

    public YesNo getIsShow() {
        return isShow;
    }
}
