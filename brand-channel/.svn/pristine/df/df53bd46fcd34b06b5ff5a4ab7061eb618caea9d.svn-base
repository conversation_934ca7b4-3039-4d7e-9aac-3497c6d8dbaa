package com.pay.brand.channel.core.event.subscribe;

import java.util.Date;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.starter.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.starter.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import com.pay.brand.channel.common.bean.dto.subscriber.TransFlowNotifyVo;
import com.pay.brand.channel.common.event.dto.TransExtendFlowDto;
import com.pay.brand.channel.core.biz.data.DataProcessBiz;
import com.pay.brand.channel.core.infrastructure.assembler.TransExtendFlowAssembler;
import com.pay.brand.channel.core.infrastructure.enums.ApiCode;
import com.pay.brand.channel.core.infrastructure.enums.DataStatus;
import com.pay.brand.channel.core.repository.entity.BcDataFlow;
import com.pay.frame.common.base.enums.Origin;
import com.pay.frame.common.base.util.JsonUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 交易流水处理 Biz
 *
 * <AUTHOR>
 * @date 2022-02-14 13:10:57
 **/
@Slf4j
@Validated
@Component
@RocketMQMessageListener(topic = "TRANS_EXTEND_FLOW_TOPIC", consumerGroup = "BC_TRANS_EXTEND_FLOW_GROUP",
        consumeThreadMax = 20)
public class TransExtendFlowSubscriber implements RocketMQListener<TransExtendFlowDto> {

    @Resource
    private DataProcessBiz dataProcessBiz;

    @Resource
    private TransExtendFlowAssembler transExtendFlowAssembler;


    @Override
    public void onMessage(TransExtendFlowDto dto) {
        log.info("subscriber data of 【{}】 on message start 【{}】",
                this.getClass().getSimpleName(), JsonUtils.bean2Json(dto));
        BcDataFlow dataFlow = new BcDataFlow();
        dataFlow.setStatus(DataStatus.INIT.name());
        dataFlow.setCreateTime(new Date());
        dataFlow.setOptimistic(0);
//        dataFlow.setBrand(dto.getBrand());
        dataFlow.setPrNo(dto.getFlowId());
        dataFlow.setPrTime(dto.getTransTime());
        dataFlow.setOrgData(JsonUtils.bean2Json(dto));

        TransFlowNotifyVo vo = transExtendFlowAssembler.toTransFlowVo(dto);

        dataFlow.setData(JsonUtils.bean2Json(vo));
        if (Origin.isEndPyp(dto.getOrigin())) {
            dataFlow.setApiCode(ApiCode.PYP_TRANS_FLOW_NOTIFY.getCode());
        } else if (Origin.isEndLklSm(dto.getOrigin())) {
            dataFlow.setApiCode(ApiCode.LKLSM_TRANS_FLOW_NOTIFY.getCode());
        } else {
            dataFlow.setApiCode(ApiCode.TRANS_FLOW_NOTIFY.getCode());
        }
        dataProcessBiz.dataSubscribe(dataFlow, dto.getAgentNo());

        log.info("subscriber data of 【{}】 on message success 【{}】",
                this.getClass().getSimpleName(), JsonUtils.bean2Json(dataFlow));
    }
}
