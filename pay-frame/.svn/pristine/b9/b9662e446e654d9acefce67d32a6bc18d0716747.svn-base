package com.pay.frame.common.base.enums;

import lombok.Getter;

/**
 * 品牌标识
 *
 * <AUTHOR>
 */
public enum Origin {
    ZF("中付", "547"),
    LS("乐刷", "590"),
    YS("银盛", "526"),
    MAX("通用版", "000"),

    PYP("通用版碰一碰", "600"),
    PYPYS("银盛碰一碰", "626"),
    PYPGT("国通碰一碰", "657"),


    LKLSM("拉卡拉SM", "736"),
    HFSM("汇付SM", "756"),
    ;

    public static boolean isEndPyp(String origin) {
        return PYPYS.name().equals(origin) || PYPGT.name().equals(origin);
    }

    public static boolean isEndLklSm(String origin) {
        return LKLSM.name().equals(origin);
    }

    public static boolean isEndSd(String origin) {
        return ZF.name().equals(origin) || LS.name().equals(origin) || YS.name().equals(origin);
    }

    public static boolean isEndSm(String origin) {
        return LKLSM.name().equals(origin) || HFSM.name().equals(origin);
    }

    private String desc;
    @Getter
    private String custPrefix;


    public String getDesc() {
        return this.desc;
    }


    Origin(String desc, String custPrefix) {
        this.desc = desc;
        this.custPrefix = custPrefix;
    }

}
